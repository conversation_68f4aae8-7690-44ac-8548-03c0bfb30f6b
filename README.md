# TIMO智能闹钟项目

## 项目简介

TIMO智能闹钟是一个基于ESP32-S3和ESP32-C2的双设备智能终端系统，集成了语音交互、环境监测、氛围灯效、时间管理等多种功能的现代化智能闹钟。

## 主要特性

### 硬件特性
- **双芯片架构**: ESP32-S3主体 + ESP32-C2底座
- **圆形触摸屏**: 480×480像素LCD显示屏
- **多传感器**: 温湿度、光照、CO2、姿态传感器
- **音频系统**: 双麦克风阵列 + 3W扬声器
- **氛围灯效**: 30颗WS2812 RGB LED
- **存储扩展**: 支持32GB TF卡
- **无线充电**: 10W无线充电接收

### 软件特性
- **语音交互**: 支持自定义唤醒词和连续对话
- **时间管理**: 多闹钟系统和番茄时钟
- **环境监测**: 实时监测并预警
- **氛围场景**: 多种智能灯光音效场景
- **网络功能**: WiFi连接和云端服务
- **UI系统**: 基于LVGL的圆形屏幕适配

## 项目结构

```
Timo_main/
├── doc/                          # 文档目录
│   ├── README.md                 # 产品设计文档
│   ├── pin_conflict_analysis.md  # 引脚冲突分析
│   └── software_user_manual.md   # 软件使用说明
├── esp_idf/                      # 主体设备代码
│   ├── main/                     # 主程序
│   ├── components/               # 组件库
│   │   └── hardware/             # 硬件抽象层
│   └── base_station/             # 底座设备代码
└── python/                       # Python工具和测试
```

## 硬件架构

### 主体设备 (ESP32-S3)
- **主控**: ESP32-S3N16R8 (16MB Flash + 8MB PSRAM)
- **显示**: 480×480圆形LCD + GT911触摸
- **传感器**: AHT30、BH1750、SGP30、QMI8658
- **音频**: ES7210输入 + ES8311输出
- **存储**: TF卡槽 + PCF85063 RTC
- **扩展**: TCA9554PWR GPIO扩展芯片

### 底座设备 (ESP32-C2)
- **主控**: ESP32-C2 (2MB Flash)
- **灯效**: WS2812 RGB灯带 (30颗LED)
- **传感**: 声音传感器
- **交互**: 用户按键 + 状态指示灯
- **充电**: 15W无线充电发射

## 引脚分配

### 关键引脚冲突解决
1. **I2C总线共享**: GPIO7(SCL) + GPIO15(SDA) 连接9个I2C设备
2. **SPI接口复用**: GPIO1/GPIO2 用于LCD命令和SD卡接口
3. **GPIO扩展**: 通过TCA9554PWR扩展控制信号
4. **⚠️ 蜂鸣器引脚**: 原设计EXIO8不存在，已改为EXIO0

详细引脚分配请参考 [引脚冲突分析文档](doc/pin_conflict_analysis.md)

## 软件架构

### 开发环境
- **框架**: ESP-IDF v5.0+
- **语言**: C/C++
- **UI库**: LVGL
- **构建系统**: CMake

### 核心模块
1. **硬件抽象层**: 统一的硬件接口
2. **传感器驱动**: 多传感器数据采集
3. **显示系统**: LCD驱动和UI渲染
4. **音频系统**: 录音播放和语音处理
5. **通信系统**: 蓝牙和WiFi连接
6. **应用层**: 闹钟、番茄时钟、环境监测等

## 快速开始

### 环境准备
```bash
# 安装ESP-IDF
git clone --recursive https://github.com/espressif/esp-idf.git
cd esp-idf
./install.sh
. ./export.sh
```

### 编译主体设备
```bash
cd esp_idf
idf.py set-target esp32s3
idf.py build
idf.py flash monitor
```

### 编译底座设备
```bash
cd esp_idf/base_station
idf.py set-target esp32c2
idf.py build
idf.py flash monitor
```

## 功能演示

### 基本功能
- [x] 硬件初始化和检测
- [x] LCD显示和触摸交互
- [x] 传感器数据采集
- [x] I2C/SPI总线管理
- [x] GPIO扩展芯片控制
- [ ] 音频录放功能
- [ ] 蓝牙通信协议
- [ ] WiFi网络连接
- [ ] UI界面系统

### 高级功能
- [ ] 语音唤醒和识别
- [ ] 多闹钟管理
- [ ] 番茄时钟
- [ ] 环境监测预警
- [ ] 氛围场景控制
- [ ] 云端服务集成

## 开发进度

### 已完成 ✅
1. **项目架构设计**: 完整的ESP-IDF项目结构
2. **引脚冲突分析**: 识别并解决硬件冲突问题
3. **硬件抽象层**: 完整的HAL驱动框架
4. **I2C总线驱动**: 支持多设备共享和互斥访问
5. **GPIO扩展驱动**: TCA9554PWR完整驱动
6. **传感器驱动**: AHT30、BH1750、SGP30、QMI8658
7. **LCD驱动**: ST7701显示屏和背光控制
8. **触摸屏驱动**: GT911触摸控制器
9. **SPI总线驱动**: LCD和SD卡复用管理
10. **软件使用说明**: 详细的用户手册

### 进行中 🚧
- 系统核心服务开发
- 用户界面系统开发
- 时间管理功能开发

### 待开发 📋
- 传感器数据采集系统
- 音频系统开发
- 蓝牙通信系统
- 底座控制系统开发
- 氛围场景系统开发
- 任务管理与番茄时钟
- WiFi配网与网络服务
- 存储管理系统
- 系统集成测试

## 技术亮点

### 硬件设计
- **引脚复用优化**: 通过GPIO扩展芯片解决引脚不足
- **总线共享**: I2C和SPI总线的高效复用
- **双芯片协作**: 主体和底座的分布式架构

### 软件设计
- **模块化架构**: 清晰的分层设计
- **硬件抽象**: 统一的HAL接口
- **错误处理**: 完善的异常处理机制
- **资源管理**: 互斥锁和队列的合理使用

## 贡献指南

### 开发规范
- 遵循ESP-IDF编码规范
- 使用统一的注释格式
- 提交前进行代码格式化
- 编写相应的测试用例

### 提交流程
1. Fork项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request
5. 代码审查和合并

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 联系我们

- **项目主页**: https://github.com/timo-clock/firmware
- **技术支持**: <EMAIL>
- **问题反馈**: https://github.com/timo-clock/firmware/issues
- **文档中心**: https://docs.timo-clock.com

---

**TIMO智能闹钟** - 让时间更智能，让生活更美好 ⏰✨
