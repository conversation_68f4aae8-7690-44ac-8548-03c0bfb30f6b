/**
 * @file system_manager.c
 * @brief TIMO系统管理器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "system_manager.h"
#include "config_manager.h"
#include "event_manager.h"
#include "power_manager.h"
#include "storage_manager.h"
#include "network_manager.h"
#include "task_scheduler.h"
#include "system_monitor.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_timer.h"
#include "esp_sleep.h"
#include "nvs_flash.h"

static const char *TAG = "SYSTEM_MANAGER";

/* 系统管理器状态 */
static bool g_system_initialized = false;
static bool g_system_running = false;
static system_config_t g_system_config;
static system_status_t g_system_status;
static system_event_callback_t g_event_callback = NULL;

/* 任务句柄 */
static TaskHandle_t g_system_task_handle = NULL;

/* 默认配置 */
static const system_config_t DEFAULT_CONFIG = {
    .brightness = 50,
    .volume = 30,
    .auto_brightness = true,
    .sleep_mode_enabled = true,
    .sleep_timeout_ms = 300000,  // 5分钟
    .wifi_enabled = true,
    .bluetooth_enabled = true,
    .timezone = "Asia/Shanghai",
    .language = "zh-CN"
};

/**
 * @brief 系统状态更新任务
 */
static void system_status_task(void *pvParameters)
{
    ESP_LOGI(TAG, "系统状态监控任务启动");
    
    while (g_system_running) {
        // 更新系统状态
        g_system_status.uptime_ms = esp_timer_get_time() / 1000;
        g_system_status.free_heap = esp_get_free_heap_size();
        g_system_status.min_free_heap = esp_get_minimum_free_heap_size();
        
        // 更新电池状态
        power_manager_update_status();
        g_system_status.battery_level = power_manager_get_battery_level();
        g_system_status.charging = power_manager_is_charging();
        
        // 更新网络状态
        g_system_status.wifi_connected = network_manager_is_wifi_connected();
        g_system_status.bluetooth_connected = network_manager_is_bluetooth_connected();
        g_system_status.rssi = network_manager_get_wifi_rssi();
        
        // 检查低电量
        if (g_system_status.battery_level <= 10 && !g_system_status.charging) {
            if (g_system_status.battery_level <= 5) {
                system_manager_send_event(SYSTEM_EVENT_CRITICAL_BATTERY, NULL);
            } else {
                system_manager_send_event(SYSTEM_EVENT_LOW_BATTERY, NULL);
            }
        }
        
        // 检查充电状态变化
        static bool last_charging_state = false;
        if (g_system_status.charging != last_charging_state) {
            if (g_system_status.charging) {
                system_manager_send_event(SYSTEM_EVENT_CHARGING_START, NULL);
            } else {
                system_manager_send_event(SYSTEM_EVENT_CHARGING_STOP, NULL);
            }
            last_charging_state = g_system_status.charging;
        }
        
        // 延时1秒
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    
    vTaskDelete(NULL);
}

/**
 * @brief 初始化系统管理器
 */
esp_err_t system_manager_init(void)
{
    if (g_system_initialized) {
        ESP_LOGW(TAG, "系统管理器已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化系统管理器...");
    
    // 初始化系统状态
    memset(&g_system_status, 0, sizeof(g_system_status));
    g_system_status.state = SYSTEM_STATE_INIT;
    
    // 加载系统配置
    esp_err_t ret = config_manager_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "配置管理器初始化失败");
        return ret;
    }
    
    ret = config_manager_load_system_config(&g_system_config);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "加载系统配置失败，使用默认配置");
        memcpy(&g_system_config, &DEFAULT_CONFIG, sizeof(system_config_t));
        config_manager_save_system_config(&g_system_config);
    }
    
    // 初始化各个管理器
    ret = event_manager_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "事件管理器初始化失败");
        return ret;
    }
    
    ret = power_manager_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "电源管理器初始化失败");
        return ret;
    }
    
    ret = storage_manager_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "存储管理器初始化失败");
        return ret;
    }
    
    ret = network_manager_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "网络管理器初始化失败");
        return ret;
    }
    
    ret = task_scheduler_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "任务调度器初始化失败");
        return ret;
    }
    
    ret = system_monitor_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "系统监控器初始化失败");
        return ret;
    }
    
    g_system_initialized = true;
    g_system_status.state = SYSTEM_STATE_STARTING;
    
    ESP_LOGI(TAG, "系统管理器初始化完成");
    return ESP_OK;
}

/**
 * @brief 启动系统管理器
 */
esp_err_t system_manager_start(void)
{
    if (!g_system_initialized) {
        ESP_LOGE(TAG, "系统管理器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (g_system_running) {
        ESP_LOGW(TAG, "系统管理器已启动");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "启动系统管理器...");
    
    // 启动各个管理器
    esp_err_t ret = event_manager_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "事件管理器启动失败");
        return ret;
    }
    
    ret = power_manager_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "电源管理器启动失败");
        return ret;
    }
    
    ret = storage_manager_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "存储管理器启动失败");
        return ret;
    }
    
    if (g_system_config.wifi_enabled) {
        ret = network_manager_start_wifi();
        if (ret != ESP_OK) {
            ESP_LOGW(TAG, "WiFi启动失败");
        }
    }
    
    if (g_system_config.bluetooth_enabled) {
        ret = network_manager_start_bluetooth();
        if (ret != ESP_OK) {
            ESP_LOGW(TAG, "蓝牙启动失败");
        }
    }
    
    ret = task_scheduler_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "任务调度器启动失败");
        return ret;
    }
    
    ret = system_monitor_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "系统监控器启动失败");
        return ret;
    }
    
    // 创建系统状态监控任务
    BaseType_t task_ret = xTaskCreate(
        system_status_task,
        "system_status",
        4096,
        NULL,
        5,
        &g_system_task_handle
    );
    
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "创建系统状态任务失败");
        return ESP_ERR_NO_MEM;
    }
    
    g_system_running = true;
    g_system_status.state = SYSTEM_STATE_RUNNING;
    
    // 发送启动事件
    system_manager_send_event(SYSTEM_EVENT_STARTUP, NULL);
    
    ESP_LOGI(TAG, "系统管理器启动完成");
    return ESP_OK;
}

/**
 * @brief 停止系统管理器
 */
esp_err_t system_manager_stop(void)
{
    if (!g_system_running) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "停止系统管理器...");
    
    g_system_running = false;
    g_system_status.state = SYSTEM_STATE_SHUTDOWN;
    
    // 发送关机事件
    system_manager_send_event(SYSTEM_EVENT_SHUTDOWN, NULL);
    
    // 停止系统状态任务
    if (g_system_task_handle) {
        vTaskDelete(g_system_task_handle);
        g_system_task_handle = NULL;
    }
    
    // 停止各个管理器
    system_monitor_stop();
    task_scheduler_stop();
    network_manager_stop();
    storage_manager_stop();
    power_manager_stop();
    event_manager_stop();
    
    ESP_LOGI(TAG, "系统管理器停止完成");
    return ESP_OK;
}

/**
 * @brief 获取系统状态
 */
system_status_t* system_manager_get_status(void)
{
    return &g_system_status;
}

/**
 * @brief 获取系统配置
 */
system_config_t* system_manager_get_config(void)
{
    return &g_system_config;
}

/**
 * @brief 设置系统配置
 */
esp_err_t system_manager_set_config(const system_config_t *config)
{
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }
    
    memcpy(&g_system_config, config, sizeof(system_config_t));
    return ESP_OK;
}

/**
 * @brief 保存系统配置
 */
esp_err_t system_manager_save_config(void)
{
    return config_manager_save_system_config(&g_system_config);
}

/**
 * @brief 注册系统事件回调
 */
esp_err_t system_manager_register_event_callback(system_event_callback_t callback)
{
    g_event_callback = callback;
    return ESP_OK;
}

/**
 * @brief 发送系统事件
 */
esp_err_t system_manager_send_event(system_event_t event, void *data)
{
    if (g_event_callback) {
        g_event_callback(event, data);
    }
    
    return event_manager_post_event(EVENT_TYPE_SYSTEM, event, data);
}

/**
 * @brief 系统重启
 */
void system_manager_restart(void)
{
    ESP_LOGI(TAG, "系统重启...");
    system_manager_send_event(SYSTEM_EVENT_SHUTDOWN, NULL);
    vTaskDelay(pdMS_TO_TICKS(1000));
    esp_restart();
}

/**
 * @brief 系统关机
 */
void system_manager_shutdown(void)
{
    ESP_LOGI(TAG, "系统关机...");
    system_manager_stop();
    esp_deep_sleep_start();
}

/**
 * @brief 进入睡眠模式
 */
void system_manager_sleep(uint32_t sleep_time_ms)
{
    ESP_LOGI(TAG, "进入睡眠模式，时间: %d ms", sleep_time_ms);
    
    g_system_status.state = SYSTEM_STATE_SLEEP;
    system_manager_send_event(SYSTEM_EVENT_SLEEP, &sleep_time_ms);
    
    if (sleep_time_ms > 0) {
        esp_sleep_enable_timer_wakeup(sleep_time_ms * 1000);
    }
    
    esp_light_sleep_start();
    
    g_system_status.state = SYSTEM_STATE_RUNNING;
    system_manager_send_event(SYSTEM_EVENT_WAKEUP, NULL);
}

/**
 * @brief 恢复出厂设置
 */
esp_err_t system_manager_factory_reset(void)
{
    ESP_LOGI(TAG, "恢复出厂设置...");
    
    system_manager_send_event(SYSTEM_EVENT_FACTORY_RESET, NULL);
    
    // 清除NVS数据
    esp_err_t ret = nvs_flash_erase();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "清除NVS失败");
        return ret;
    }
    
    // 重新初始化NVS
    ret = nvs_flash_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "重新初始化NVS失败");
        return ret;
    }
    
    // 恢复默认配置
    memcpy(&g_system_config, &DEFAULT_CONFIG, sizeof(system_config_t));
    config_manager_save_system_config(&g_system_config);
    
    ESP_LOGI(TAG, "恢复出厂设置完成，系统将重启");
    vTaskDelay(pdMS_TO_TICKS(1000));
    esp_restart();
    
    return ESP_OK;
}

/**
 * @brief 获取系统运行时间
 */
uint64_t system_manager_get_uptime(void)
{
    return esp_timer_get_time() / 1000;
}

/**
 * @brief 获取系统版本信息
 */
const char* system_manager_get_version(void)
{
    return "TIMO v1.0.0";
}
