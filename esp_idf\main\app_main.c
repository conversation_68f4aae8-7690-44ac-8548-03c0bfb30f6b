/**
 * @file app_main.c
 * @brief TIMO智能闹钟应用程序主逻辑
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "app_main.h"
#include "hardware_hal.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_system.h"
#include "esp_sleep.h"

static const char *TAG = "APP_MAIN";

/* 全局事件组和队列 */
EventGroupHandle_t g_system_event_group = NULL;
QueueHandle_t g_sensor_data_queue = NULL;
QueueHandle_t g_audio_event_queue = NULL;
QueueHandle_t g_ui_event_queue = NULL;

/* 任务句柄 */
static TaskHandle_t h_hardware_init_task = NULL;
static TaskHandle_t h_system_monitor_task = NULL;

/**
 * @brief 硬件初始化任务
 */
static void hardware_init_task(void *pvParameters)
{
    ESP_LOGI(TAG, "开始硬件初始化...");
    
    // 初始化所有硬件组件
    ESP_LOGI(TAG, "开始硬件初始化...");
    esp_err_t ret = hardware_init_all();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "硬件初始化成功");
        xEventGroupSetBits(g_system_event_group, SYSTEM_EVENT_SENSORS_READY);
        xEventGroupSetBits(g_system_event_group, SYSTEM_EVENT_AUDIO_READY);
        xEventGroupSetBits(g_system_event_group, SYSTEM_EVENT_UI_READY);
    } else {
        ESP_LOGE(TAG, "硬件初始化失败，系统将以降级模式运行");
    }

    // 打印硬件状态
    hardware_print_status();

    // TODO: 初始化RTC时钟
    ESP_LOGI(TAG, "初始化RTC时钟...");
    vTaskDelay(pdMS_TO_TICKS(100));

    // TODO: 初始化蓝牙
    ESP_LOGI(TAG, "初始化蓝牙...");
    vTaskDelay(pdMS_TO_TICKS(200));
    xEventGroupSetBits(g_system_event_group, SYSTEM_EVENT_BLUETOOTH_READY);

    // TODO: 初始化WiFi
    ESP_LOGI(TAG, "初始化WiFi...");
    vTaskDelay(pdMS_TO_TICKS(200));
    
    ESP_LOGI(TAG, "硬件初始化完成");
    
    // 删除任务
    vTaskDelete(NULL);
}

/**
 * @brief 系统监控任务
 */
static void system_monitor_task(void *pvParameters)
{
    ESP_LOGI(TAG, "系统监控任务启动");
    
    while (1) {
        // 等待所有系统组件就绪
        EventBits_t bits = xEventGroupWaitBits(
            g_system_event_group,
            SYSTEM_EVENT_SENSORS_READY | SYSTEM_EVENT_AUDIO_READY | 
            SYSTEM_EVENT_BLUETOOTH_READY | SYSTEM_EVENT_UI_READY,
            pdFALSE,
            pdTRUE,
            pdMS_TO_TICKS(1000)
        );
        
        if ((bits & (SYSTEM_EVENT_SENSORS_READY | SYSTEM_EVENT_AUDIO_READY | 
                    SYSTEM_EVENT_BLUETOOTH_READY | SYSTEM_EVENT_UI_READY)) == 
            (SYSTEM_EVENT_SENSORS_READY | SYSTEM_EVENT_AUDIO_READY | 
             SYSTEM_EVENT_BLUETOOTH_READY | SYSTEM_EVENT_UI_READY)) {
            ESP_LOGI(TAG, "所有系统组件已就绪");
            break;
        }
        
        ESP_LOGI(TAG, "等待系统组件就绪... (0x%08x)", bits);
    }
    
    // 系统监控循环
    while (1) {
        // 监控内存使用情况
        size_t free_heap = esp_get_free_heap_size();
        size_t min_free_heap = esp_get_minimum_free_heap_size();
        
        ESP_LOGI(TAG, "内存状态 - 当前空闲: %d bytes, 最小空闲: %d bytes", 
                 free_heap, min_free_heap);
        
        // 监控任务状态
        UBaseType_t task_count = uxTaskGetNumberOfTasks();
        ESP_LOGI(TAG, "当前任务数量: %d", task_count);
        
        // 延时10秒
        vTaskDelay(pdMS_TO_TICKS(10000));
    }
}

/**
 * @brief 应用程序主启动函数
 */
esp_err_t app_main_start(void)
{
    ESP_LOGI(TAG, "启动TIMO智能闹钟应用程序");
    
    // 创建全局事件组
    g_system_event_group = xEventGroupCreate();
    if (g_system_event_group == NULL) {
        ESP_LOGE(TAG, "创建系统事件组失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建全局队列
    g_sensor_data_queue = xQueueCreate(10, sizeof(void*));
    g_audio_event_queue = xQueueCreate(5, sizeof(void*));
    g_ui_event_queue = xQueueCreate(10, sizeof(void*));
    
    if (!g_sensor_data_queue || !g_audio_event_queue || !g_ui_event_queue) {
        ESP_LOGE(TAG, "创建全局队列失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建硬件初始化任务
    BaseType_t ret = xTaskCreate(
        hardware_init_task,
        "hardware_init",
        TASK_STACK_SIZE_LARGE,
        NULL,
        TASK_PRIORITY_HIGH,
        &h_hardware_init_task
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建硬件初始化任务失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建系统监控任务
    ret = xTaskCreate(
        system_monitor_task,
        "system_monitor",
        TASK_STACK_SIZE_MEDIUM,
        NULL,
        TASK_PRIORITY_LOW,
        &h_system_monitor_task
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建系统监控任务失败");
        return ESP_ERR_NO_MEM;
    }
    
    ESP_LOGI(TAG, "应用程序启动完成");
    return ESP_OK;
}

/**
 * @brief 获取系统运行时间（毫秒）
 */
uint64_t app_get_uptime_ms(void)
{
    return esp_timer_get_time() / 1000;
}

/**
 * @brief 系统重启
 */
void app_system_restart(void)
{
    ESP_LOGI(TAG, "系统重启...");
    esp_restart();
}

/**
 * @brief 进入深度睡眠模式
 */
void app_enter_deep_sleep(uint64_t sleep_time_us)
{
    ESP_LOGI(TAG, "进入深度睡眠模式，睡眠时间: %llu us", sleep_time_us);
    esp_sleep_enable_timer_wakeup(sleep_time_us);
    esp_deep_sleep_start();
}
