/**
 * @file storage_manager.c
 * @brief TIMO存储管理器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "storage_manager.h"
#include "hardware_hal.h"
#include "esp_log.h"

static const char *TAG = "STORAGE_MANAGER";

esp_err_t storage_manager_init(void)
{
    ESP_LOGI(TAG, "初始化存储管理器...");
    return ESP_OK;
}

esp_err_t storage_manager_start(void)
{
    ESP_LOGI(TAG, "启动存储管理器...");
    return ESP_OK;
}

esp_err_t storage_manager_stop(void)
{
    ESP_LOGI(TAG, "停止存储管理器...");
    return ESP_OK;
}
