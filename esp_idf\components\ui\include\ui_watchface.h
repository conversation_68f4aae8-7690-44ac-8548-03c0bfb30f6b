/**
 * @file ui_watchface.h
 * @brief TIMO表盘页面头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef UI_WATCHFACE_H
#define UI_WATCHFACE_H

#include "esp_err.h"
#include "lvgl.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 创建表盘页面
 * @return lv_obj_t* 页面对象
 */
lv_obj_t* ui_watchface_create(void);

/**
 * @brief 更新时间显示
 * @param hour 小时
 * @param minute 分钟
 * @param second 秒
 * @return esp_err_t 
 */
esp_err_t ui_watchface_update_time(uint8_t hour, uint8_t minute, uint8_t second);

/**
 * @brief 更新日期显示
 * @param year 年
 * @param month 月
 * @param day 日
 * @param weekday 星期
 * @return esp_err_t 
 */
esp_err_t ui_watchface_update_date(uint16_t year, uint8_t month, uint8_t day, uint8_t weekday);

/**
 * @brief 更新电池状态显示
 * @param level 电量百分比
 * @param charging 是否充电
 * @return esp_err_t 
 */
esp_err_t ui_watchface_update_battery(uint8_t level, bool charging);

/**
 * @brief 更新环境数据显示
 * @param temperature 温度
 * @param humidity 湿度
 * @return esp_err_t 
 */
esp_err_t ui_watchface_update_environment(float temperature, float humidity);

/**
 * @brief 设置表盘样式
 * @param style_id 样式ID
 * @return esp_err_t 
 */
esp_err_t ui_watchface_set_style(uint8_t style_id);

#ifdef __cplusplus
}
#endif

#endif /* UI_WATCHFACE_H */
