/**
 * @file event_manager.c
 * @brief TIMO事件管理器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "event_manager.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

static const char *TAG = "EVENT_MANAGER";

/* 事件管理器配置 */
#define EVENT_QUEUE_SIZE        50
#define EVENT_TASK_STACK_SIZE   4096
#define EVENT_TASK_PRIORITY     5

/* 事件管理器状态 */
static bool g_event_manager_initialized = false;
static bool g_event_manager_running = false;
static QueueHandle_t g_event_queue = NULL;
static TaskHandle_t g_event_task_handle = NULL;

/* 事件处理器数组 */
static event_handler_t g_event_handlers[EVENT_TYPE_MAX] = {0};

/* 事件类型名称 */
static const char* EVENT_TYPE_NAMES[] = {
    "SYSTEM",
    "UI",
    "SENSOR",
    "AUDIO",
    "NETWORK",
    "ALARM",
    "TASK",
    "BLUETOOTH",
    "STORAGE",
    "POWER"
};

/**
 * @brief 事件处理任务
 */
static void event_handler_task(void *pvParameters)
{
    ESP_LOGI(TAG, "事件处理任务启动");
    
    event_t event;
    
    while (g_event_manager_running) {
        // 从队列中获取事件
        if (xQueueReceive(g_event_queue, &event, pdMS_TO_TICKS(100)) == pdTRUE) {
            ESP_LOGD(TAG, "处理事件: 类型=%s, ID=%d, 时间戳=%llu", 
                     EVENT_TYPE_NAMES[event.type], event.id, event.timestamp);
            
            // 检查事件类型是否有效
            if (event.type < EVENT_TYPE_MAX && g_event_handlers[event.type]) {
                // 调用事件处理器
                g_event_handlers[event.type](&event);
            } else {
                ESP_LOGW(TAG, "未找到事件处理器: 类型=%d", event.type);
            }
            
            // 释放事件数据内存（如果有的话）
            if (event.data && event.data_size > 0) {
                free(event.data);
            }
        }
    }
    
    ESP_LOGI(TAG, "事件处理任务结束");
    vTaskDelete(NULL);
}

/**
 * @brief 初始化事件管理器
 */
esp_err_t event_manager_init(void)
{
    if (g_event_manager_initialized) {
        ESP_LOGW(TAG, "事件管理器已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化事件管理器...");
    
    // 创建事件队列
    g_event_queue = xQueueCreate(EVENT_QUEUE_SIZE, sizeof(event_t));
    if (!g_event_queue) {
        ESP_LOGE(TAG, "创建事件队列失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 清空事件处理器数组
    memset(g_event_handlers, 0, sizeof(g_event_handlers));
    
    g_event_manager_initialized = true;
    ESP_LOGI(TAG, "事件管理器初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 启动事件管理器
 */
esp_err_t event_manager_start(void)
{
    if (!g_event_manager_initialized) {
        ESP_LOGE(TAG, "事件管理器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (g_event_manager_running) {
        ESP_LOGW(TAG, "事件管理器已启动");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "启动事件管理器...");
    
    // 创建事件处理任务
    BaseType_t ret = xTaskCreate(
        event_handler_task,
        "event_handler",
        EVENT_TASK_STACK_SIZE,
        NULL,
        EVENT_TASK_PRIORITY,
        &g_event_task_handle
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建事件处理任务失败");
        return ESP_ERR_NO_MEM;
    }
    
    g_event_manager_running = true;
    ESP_LOGI(TAG, "事件管理器启动完成");
    
    return ESP_OK;
}

/**
 * @brief 停止事件管理器
 */
esp_err_t event_manager_stop(void)
{
    if (!g_event_manager_running) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "停止事件管理器...");
    
    g_event_manager_running = false;
    
    // 等待任务结束
    if (g_event_task_handle) {
        vTaskDelete(g_event_task_handle);
        g_event_task_handle = NULL;
    }
    
    // 清空事件队列
    event_manager_clear_queue();
    
    ESP_LOGI(TAG, "事件管理器停止完成");
    return ESP_OK;
}

/**
 * @brief 注册事件处理器
 */
esp_err_t event_manager_register_handler(event_type_t type, event_handler_t handler)
{
    if (type >= EVENT_TYPE_MAX) {
        ESP_LOGE(TAG, "无效的事件类型: %d", type);
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!handler) {
        ESP_LOGE(TAG, "事件处理器不能为空");
        return ESP_ERR_INVALID_ARG;
    }
    
    g_event_handlers[type] = handler;
    ESP_LOGI(TAG, "注册事件处理器: 类型=%s", EVENT_TYPE_NAMES[type]);
    
    return ESP_OK;
}

/**
 * @brief 注销事件处理器
 */
esp_err_t event_manager_unregister_handler(event_type_t type)
{
    if (type >= EVENT_TYPE_MAX) {
        ESP_LOGE(TAG, "无效的事件类型: %d", type);
        return ESP_ERR_INVALID_ARG;
    }
    
    g_event_handlers[type] = NULL;
    ESP_LOGI(TAG, "注销事件处理器: 类型=%s", EVENT_TYPE_NAMES[type]);
    
    return ESP_OK;
}

/**
 * @brief 发送事件
 */
esp_err_t event_manager_post_event(event_type_t type, uint32_t id, void *data)
{
    return event_manager_post_event_with_size(type, id, data, 0);
}

/**
 * @brief 发送事件（带数据大小）
 */
esp_err_t event_manager_post_event_with_size(event_type_t type, uint32_t id, void *data, size_t data_size)
{
    if (!g_event_manager_initialized) {
        ESP_LOGE(TAG, "事件管理器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (type >= EVENT_TYPE_MAX) {
        ESP_LOGE(TAG, "无效的事件类型: %d", type);
        return ESP_ERR_INVALID_ARG;
    }
    
    event_t event = {
        .type = type,
        .id = id,
        .data = data,
        .data_size = data_size,
        .timestamp = esp_timer_get_time()
    };
    
    // 如果需要复制数据
    if (data && data_size > 0) {
        void *data_copy = malloc(data_size);
        if (!data_copy) {
            ESP_LOGE(TAG, "分配事件数据内存失败");
            return ESP_ERR_NO_MEM;
        }
        memcpy(data_copy, data, data_size);
        event.data = data_copy;
    }
    
    // 发送事件到队列
    BaseType_t ret = xQueueSend(g_event_queue, &event, pdMS_TO_TICKS(100));
    if (ret != pdTRUE) {
        ESP_LOGE(TAG, "发送事件失败: 队列已满");
        if (event.data && data_size > 0) {
            free(event.data);
        }
        return ESP_ERR_TIMEOUT;
    }
    
    ESP_LOGD(TAG, "发送事件: 类型=%s, ID=%d", EVENT_TYPE_NAMES[type], id);
    return ESP_OK;
}

/**
 * @brief 发送高优先级事件
 */
esp_err_t event_manager_post_urgent_event(event_type_t type, uint32_t id, void *data)
{
    if (!g_event_manager_initialized) {
        ESP_LOGE(TAG, "事件管理器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (type >= EVENT_TYPE_MAX) {
        ESP_LOGE(TAG, "无效的事件类型: %d", type);
        return ESP_ERR_INVALID_ARG;
    }
    
    event_t event = {
        .type = type,
        .id = id,
        .data = data,
        .data_size = 0,
        .timestamp = esp_timer_get_time()
    };
    
    // 发送到队列前端（高优先级）
    BaseType_t ret = xQueueSendToFront(g_event_queue, &event, pdMS_TO_TICKS(100));
    if (ret != pdTRUE) {
        ESP_LOGE(TAG, "发送紧急事件失败: 队列已满");
        return ESP_ERR_TIMEOUT;
    }
    
    ESP_LOGI(TAG, "发送紧急事件: 类型=%s, ID=%d", EVENT_TYPE_NAMES[type], id);
    return ESP_OK;
}

/**
 * @brief 获取事件队列长度
 */
uint32_t event_manager_get_queue_length(void)
{
    if (!g_event_queue) {
        return 0;
    }
    
    return uxQueueMessagesWaiting(g_event_queue);
}

/**
 * @brief 清空事件队列
 */
esp_err_t event_manager_clear_queue(void)
{
    if (!g_event_queue) {
        return ESP_ERR_INVALID_STATE;
    }
    
    event_t event;
    while (xQueueReceive(g_event_queue, &event, 0) == pdTRUE) {
        // 释放事件数据内存
        if (event.data && event.data_size > 0) {
            free(event.data);
        }
    }
    
    ESP_LOGI(TAG, "事件队列已清空");
    return ESP_OK;
}
