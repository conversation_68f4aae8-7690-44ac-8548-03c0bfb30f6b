/**
 * @file ui_settings.c
 * @brief TIMO设置页面实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "ui_settings.h"
#include "ui_theme.h"
#include "hardware_config.h"
#include "esp_log.h"

static const char *TAG = "UI_SETTINGS";
static lv_obj_t *g_settings_page = NULL;
static lv_obj_t *g_battery_info = NULL;

lv_obj_t* ui_settings_create(void)
{
    ESP_LOGI(TAG, "创建设置页面...");
    
    g_settings_page = lv_obj_create(NULL);
    lv_obj_set_size(g_settings_page, LCD_WIDTH, LCD_HEIGHT);
    lv_obj_set_style_bg_color(g_settings_page, lv_color_hex(0x3E1E0E), 0);
    
    lv_obj_t *title = lv_label_create(g_settings_page);
    lv_label_set_text(title, "系统设置");
    lv_obj_add_style(title, ui_theme_create_label_style(18), 0);
    lv_obj_set_pos(title, 50, 50);
    
    g_battery_info = lv_label_create(g_settings_page);
    lv_label_set_text(g_battery_info, "电池: 80%");
    lv_obj_add_style(g_battery_info, ui_theme_create_label_style(14), 0);
    lv_obj_set_pos(g_battery_info, 50, 100);
    
    ESP_LOGI(TAG, "设置页面创建完成");
    return g_settings_page;
}

esp_err_t ui_settings_update_battery(uint8_t level, bool charging)
{
    if (!g_battery_info) {
        return ESP_ERR_INVALID_STATE;
    }
    
    char battery_str[32];
    if (charging) {
        snprintf(battery_str, sizeof(battery_str), "电池: %d%% (充电中)", level);
    } else {
        snprintf(battery_str, sizeof(battery_str), "电池: %d%%", level);
    }
    lv_label_set_text(g_battery_info, battery_str);
    
    return ESP_OK;
}
