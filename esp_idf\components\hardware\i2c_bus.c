/**
 * @file i2c_bus.c
 * @brief I2C总线驱动 - 处理多设备共享
 * @version 1.0.0
 * @date 2025-06-27
 * 
 * 共享设备列表：
 * - 0x18: ES8311 音频输出
 * - 0x20: TCA9554PWR GPIO扩展芯片
 * - 0x23: BH1750 光照传感器
 * - 0x38: AHT30 温湿度传感器
 * - 0x41: ES7210 音频输入
 * - 0x51: PCF85063 RTC时钟
 * - 0x58: SGP30 CO2传感器
 * - 0x5D: GT911 触摸屏
 * - 0x6A: QMI8658 姿态传感器
 */

#include "i2c_bus.h"
#include "hardware_config.h"
#include "esp_log.h"
#include "driver/i2c.h"
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"

static const char *TAG = "I2C_BUS";

/* I2C总线状态 */
static bool g_i2c_initialized = false;
static SemaphoreHandle_t g_i2c_mutex = NULL;

/* I2C设备信息 */
typedef struct {
    uint8_t addr;
    const char *name;
    bool detected;
} i2c_device_info_t;

static i2c_device_info_t g_i2c_devices[] = {
    {ES8311_I2C_ADDR,    "ES8311音频输出",     false},
    {TCA9554_I2C_ADDR,   "TCA9554GPIO扩展",    false},
    {BH1750_I2C_ADDR,    "BH1750光照传感器",   false},
    {AHT30_I2C_ADDR,     "AHT30温湿度传感器",  false},
    {ES7210_I2C_ADDR,    "ES7210音频输入",     false},
    {PCF85063_I2C_ADDR,  "PCF85063RTC时钟",    false},
    {SGP30_I2C_ADDR,     "SGP30CO2传感器",     false},
    {TOUCH_GT911_I2C_ADDR, "GT911触摸屏",      false},
    {QMI8658_I2C_ADDR,   "QMI8658姿态传感器",  false}
};

#define I2C_DEVICE_COUNT (sizeof(g_i2c_devices) / sizeof(g_i2c_devices[0]))

/**
 * @brief 检测I2C设备是否存在
 */
static bool i2c_device_probe(uint8_t addr)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (addr << 1) | I2C_MASTER_WRITE, true);
    i2c_master_stop(cmd);
    
    esp_err_t ret = i2c_master_cmd_begin(I2C_MASTER_NUM, cmd, pdMS_TO_TICKS(50));
    i2c_cmd_link_delete(cmd);
    
    return (ret == ESP_OK);
}

/**
 * @brief 扫描I2C总线上的设备
 */
static void i2c_scan_devices(void)
{
    ESP_LOGI(TAG, "扫描I2C总线设备...");
    
    int found_count = 0;
    
    for (int i = 0; i < I2C_DEVICE_COUNT; i++) {
        g_i2c_devices[i].detected = i2c_device_probe(g_i2c_devices[i].addr);
        
        if (g_i2c_devices[i].detected) {
            ESP_LOGI(TAG, "发现设备: 0x%02X - %s", 
                     g_i2c_devices[i].addr, g_i2c_devices[i].name);
            found_count++;
        } else {
            ESP_LOGW(TAG, "未发现设备: 0x%02X - %s", 
                     g_i2c_devices[i].addr, g_i2c_devices[i].name);
        }
    }
    
    ESP_LOGI(TAG, "I2C设备扫描完成，发现 %d/%d 个设备", found_count, I2C_DEVICE_COUNT);
}

/**
 * @brief 初始化I2C总线
 */
esp_err_t i2c_bus_init(void)
{
    if (g_i2c_initialized) {
        ESP_LOGW(TAG, "I2C总线已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化I2C总线...");
    
    // 创建互斥锁
    g_i2c_mutex = xSemaphoreCreateMutex();
    if (!g_i2c_mutex) {
        ESP_LOGE(TAG, "创建I2C互斥锁失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 配置I2C参数
    i2c_config_t conf = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = I2C_MASTER_SDA_IO,
        .scl_io_num = I2C_MASTER_SCL_IO,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = I2C_MASTER_FREQ_HZ,
        .clk_flags = 0
    };
    
    esp_err_t ret = i2c_param_config(I2C_MASTER_NUM, &conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C参数配置失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 安装I2C驱动
    ret = i2c_driver_install(I2C_MASTER_NUM, conf.mode, 
                            I2C_MASTER_RX_BUF_DISABLE, 
                            I2C_MASTER_TX_BUF_DISABLE, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C驱动安装失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    g_i2c_initialized = true;
    ESP_LOGI(TAG, "I2C总线初始化完成 (SCL: GPIO%d, SDA: GPIO%d, 频率: %d Hz)", 
             I2C_MASTER_SCL_IO, I2C_MASTER_SDA_IO, I2C_MASTER_FREQ_HZ);
    
    // 扫描设备
    i2c_scan_devices();
    
    return ESP_OK;
}

/**
 * @brief I2C写数据
 */
esp_err_t i2c_bus_write_bytes(uint8_t device_addr, uint8_t reg_addr, 
                              uint8_t *data, size_t len)
{
    if (!g_i2c_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (!data || len == 0) {
        return ESP_ERR_INVALID_ARG;
    }
    
    xSemaphoreTake(g_i2c_mutex, portMAX_DELAY);
    
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (device_addr << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg_addr, true);
    i2c_master_write(cmd, data, len, true);
    i2c_master_stop(cmd);
    
    esp_err_t ret = i2c_master_cmd_begin(I2C_MASTER_NUM, cmd, 
                                        pdMS_TO_TICKS(I2C_MASTER_TIMEOUT_MS));
    i2c_cmd_link_delete(cmd);
    
    xSemaphoreGive(g_i2c_mutex);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C写失败 [0x%02X:0x%02X] len=%d: %s", 
                 device_addr, reg_addr, len, esp_err_to_name(ret));
    } else {
        ESP_LOGD(TAG, "I2C写成功 [0x%02X:0x%02X] len=%d", device_addr, reg_addr, len);
    }
    
    return ret;
}

/**
 * @brief I2C读数据
 */
esp_err_t i2c_bus_read_bytes(uint8_t device_addr, uint8_t reg_addr, 
                             uint8_t *data, size_t len)
{
    if (!g_i2c_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (!data || len == 0) {
        return ESP_ERR_INVALID_ARG;
    }
    
    xSemaphoreTake(g_i2c_mutex, portMAX_DELAY);
    
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (device_addr << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg_addr, true);
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (device_addr << 1) | I2C_MASTER_READ, true);
    
    if (len > 1) {
        i2c_master_read(cmd, data, len - 1, I2C_MASTER_ACK);
    }
    i2c_master_read_byte(cmd, data + len - 1, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    
    esp_err_t ret = i2c_master_cmd_begin(I2C_MASTER_NUM, cmd, 
                                        pdMS_TO_TICKS(I2C_MASTER_TIMEOUT_MS));
    i2c_cmd_link_delete(cmd);
    
    xSemaphoreGive(g_i2c_mutex);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C读失败 [0x%02X:0x%02X] len=%d: %s", 
                 device_addr, reg_addr, len, esp_err_to_name(ret));
    } else {
        ESP_LOGD(TAG, "I2C读成功 [0x%02X:0x%02X] len=%d", device_addr, reg_addr, len);
    }
    
    return ret;
}

/**
 * @brief I2C写单个字节
 */
esp_err_t i2c_bus_write_byte(uint8_t device_addr, uint8_t reg_addr, uint8_t data)
{
    return i2c_bus_write_bytes(device_addr, reg_addr, &data, 1);
}

/**
 * @brief I2C读单个字节
 */
esp_err_t i2c_bus_read_byte(uint8_t device_addr, uint8_t reg_addr, uint8_t *data)
{
    return i2c_bus_read_bytes(device_addr, reg_addr, data, 1);
}

/**
 * @brief 检查I2C设备是否在线
 */
bool i2c_bus_device_online(uint8_t device_addr)
{
    if (!g_i2c_initialized) {
        return false;
    }
    
    for (int i = 0; i < I2C_DEVICE_COUNT; i++) {
        if (g_i2c_devices[i].addr == device_addr) {
            return g_i2c_devices[i].detected;
        }
    }
    
    return i2c_device_probe(device_addr);
}

/**
 * @brief 获取I2C设备名称
 */
const char* i2c_bus_get_device_name(uint8_t device_addr)
{
    for (int i = 0; i < I2C_DEVICE_COUNT; i++) {
        if (g_i2c_devices[i].addr == device_addr) {
            return g_i2c_devices[i].name;
        }
    }
    
    return "未知设备";
}

/**
 * @brief 重新扫描I2C设备
 */
void i2c_bus_rescan(void)
{
    if (g_i2c_initialized) {
        i2c_scan_devices();
    }
}

/**
 * @brief 反初始化I2C总线
 */
esp_err_t i2c_bus_deinit(void)
{
    if (!g_i2c_initialized) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "反初始化I2C总线...");
    
    esp_err_t ret = i2c_driver_delete(I2C_MASTER_NUM);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C驱动删除失败: %s", esp_err_to_name(ret));
    }
    
    if (g_i2c_mutex) {
        vSemaphoreDelete(g_i2c_mutex);
        g_i2c_mutex = NULL;
    }
    
    g_i2c_initialized = false;
    ESP_LOGI(TAG, "I2C总线反初始化完成");
    
    return ret;
}
