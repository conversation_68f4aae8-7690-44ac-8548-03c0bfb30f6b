/**
 * @file sensor_drivers.h
 * @brief 传感器驱动程序头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef SENSOR_DRIVERS_H
#define SENSOR_DRIVERS_H

#include "esp_err.h"
#include "hardware_hal.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化所有传感器
 * @return esp_err_t 
 */
esp_err_t sensors_init(void);

/**
 * @brief 读取AHT30温湿度传感器
 * @param data 传感器数据
 * @return esp_err_t 
 */
esp_err_t aht30_read(aht30_data_t *data);

/**
 * @brief 读取BH1750光照传感器
 * @param data 传感器数据
 * @return esp_err_t 
 */
esp_err_t bh1750_read(bh1750_data_t *data);

/**
 * @brief 读取SGP30 CO2传感器
 * @param data 传感器数据
 * @return esp_err_t 
 */
esp_err_t sgp30_read(sgp30_data_t *data);

/**
 * @brief 读取QMI8658姿态传感器
 * @param data 传感器数据
 * @return esp_err_t 
 */
esp_err_t qmi8658_read(qmi8658_data_t *data);

#ifdef __cplusplus
}
#endif

#endif /* SENSOR_DRIVERS_H */
