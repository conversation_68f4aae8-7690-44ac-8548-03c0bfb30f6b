/**
 * @file ui_manager.h
 * @brief TIMO用户界面管理器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef UI_MANAGER_H
#define UI_MANAGER_H

#include "esp_err.h"
#include "lvgl.h"

#ifdef __cplusplus
extern "C" {
#endif

/* UI页面定义 */
typedef enum {
    UI_PAGE_WATCHFACE = 0,      // 表盘页面
    UI_PAGE_MENU,               // 主菜单
    UI_PAGE_ALARM,              // 闹钟设置
    UI_PAGE_SENSOR,             // 传感器数据
    UI_PAGE_SETTINGS,           // 系统设置
    UI_PAGE_MUSIC,              // 音乐播放
    UI_PAGE_TIMER,              // 番茄时钟
    UI_PAGE_WEATHER,            // 天气信息
    UI_PAGE_MAX
} ui_page_t;

/* UI事件定义 */
typedef enum {
    UI_EVENT_PAGE_CHANGED = 0,  // 页面切换
    UI_EVENT_BUTTON_CLICKED,    // 按钮点击
    UI_EVENT_SLIDER_CHANGED,    // 滑块变化
    UI_EVENT_SWITCH_TOGGLED,    // 开关切换
    UI_EVENT_GESTURE_DETECTED,  // 手势检测
    UI_EVENT_MAX
} ui_event_t;

/* UI配置结构体 */
typedef struct {
    uint8_t brightness;         // 屏幕亮度
    uint8_t theme_id;           // 主题ID
    bool auto_brightness;       // 自动亮度
    uint32_t screen_timeout;    // 屏幕超时时间
    bool gesture_enabled;       // 手势识别
} ui_config_t;

/* UI事件回调函数类型 */
typedef void (*ui_event_callback_t)(ui_event_t event, void *data);

/**
 * @brief 初始化UI管理器
 * @return esp_err_t 
 */
esp_err_t ui_manager_init(void);

/**
 * @brief 启动UI管理器
 * @return esp_err_t 
 */
esp_err_t ui_manager_start(void);

/**
 * @brief 停止UI管理器
 * @return esp_err_t 
 */
esp_err_t ui_manager_stop(void);

/**
 * @brief 切换到指定页面
 * @param page 页面ID
 * @return esp_err_t 
 */
esp_err_t ui_manager_switch_page(ui_page_t page);

/**
 * @brief 获取当前页面
 * @return ui_page_t 
 */
ui_page_t ui_manager_get_current_page(void);

/**
 * @brief 设置屏幕亮度
 * @param brightness 亮度值 (0-100)
 * @return esp_err_t 
 */
esp_err_t ui_manager_set_brightness(uint8_t brightness);

/**
 * @brief 获取屏幕亮度
 * @return uint8_t 亮度值
 */
uint8_t ui_manager_get_brightness(void);

/**
 * @brief 设置主题
 * @param theme_id 主题ID
 * @return esp_err_t 
 */
esp_err_t ui_manager_set_theme(uint8_t theme_id);

/**
 * @brief 获取当前主题
 * @return uint8_t 主题ID
 */
uint8_t ui_manager_get_theme(void);

/**
 * @brief 注册UI事件回调
 * @param callback 回调函数
 * @return esp_err_t 
 */
esp_err_t ui_manager_register_event_callback(ui_event_callback_t callback);

/**
 * @brief 发送UI事件
 * @param event 事件类型
 * @param data 事件数据
 * @return esp_err_t 
 */
esp_err_t ui_manager_send_event(ui_event_t event, void *data);

/**
 * @brief 更新传感器数据显示
 * @param temperature 温度
 * @param humidity 湿度
 * @param lux 光照强度
 * @param co2 CO2浓度
 * @return esp_err_t 
 */
esp_err_t ui_manager_update_sensor_data(float temperature, float humidity, float lux, uint16_t co2);

/**
 * @brief 更新时间显示
 * @param hour 小时
 * @param minute 分钟
 * @param second 秒
 * @return esp_err_t 
 */
esp_err_t ui_manager_update_time(uint8_t hour, uint8_t minute, uint8_t second);

/**
 * @brief 更新电池状态显示
 * @param level 电量百分比
 * @param charging 是否充电
 * @return esp_err_t 
 */
esp_err_t ui_manager_update_battery(uint8_t level, bool charging);

/**
 * @brief 显示通知消息
 * @param title 标题
 * @param message 消息内容
 * @param duration_ms 显示时长(毫秒)
 * @return esp_err_t 
 */
esp_err_t ui_manager_show_notification(const char *title, const char *message, uint32_t duration_ms);

/**
 * @brief 显示确认对话框
 * @param title 标题
 * @param message 消息内容
 * @param callback 确认回调函数
 * @return esp_err_t 
 */
esp_err_t ui_manager_show_confirm_dialog(const char *title, const char *message, void (*callback)(bool confirmed));

#ifdef __cplusplus
}
#endif

#endif /* UI_MANAGER_H */
