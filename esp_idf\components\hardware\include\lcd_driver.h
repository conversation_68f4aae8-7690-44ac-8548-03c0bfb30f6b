/**
 * @file lcd_driver.h
 * @brief LCD显示屏驱动头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef LCD_DRIVER_H
#define LCD_DRIVER_H

#include "esp_err.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/* RGB565颜色定义 */
#define LCD_COLOR_BLACK     0x0000
#define LCD_COLOR_WHITE     0xFFFF
#define LCD_COLOR_RED       0xF800
#define LCD_COLOR_GREEN     0x07E0
#define LCD_COLOR_BLUE      0x001F
#define LCD_COLOR_YELLOW    0xFFE0
#define LCD_COLOR_CYAN      0x07FF
#define LCD_COLOR_MAGENTA   0xF81F

/**
 * @brief 初始化LCD显示屏
 * @return esp_err_t 
 */
esp_err_t lcd_init(void);

/**
 * @brief 设置背光亮度
 * @param brightness 亮度值 (0-100)
 * @return esp_err_t 
 */
esp_err_t lcd_set_backlight(uint8_t brightness);

/**
 * @brief 获取当前背光亮度
 * @return uint8_t 亮度值 (0-100)
 */
uint8_t lcd_get_backlight(void);

/**
 * @brief 在LCD上绘制位图
 * @param x 起始X坐标
 * @param y 起始Y坐标
 * @param width 宽度
 * @param height 高度
 * @param color_data RGB565颜色数据
 * @return esp_err_t 
 */
esp_err_t lcd_draw_bitmap(int x, int y, int width, int height, uint16_t *color_data);

/**
 * @brief 填充LCD屏幕
 * @param color RGB565颜色值
 * @return esp_err_t 
 */
esp_err_t lcd_fill_screen(uint16_t color);

/**
 * @brief 反初始化LCD
 * @return esp_err_t 
 */
esp_err_t lcd_deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* LCD_DRIVER_H */
