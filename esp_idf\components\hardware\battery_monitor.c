/**
 * @file battery_monitor.c
 * @brief 电池电压监控驱动程序
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "battery_monitor.h"
#include "hardware_hal.h"
#include "hardware_config.h"
#include "esp_log.h"
#include "esp_adc/adc_oneshot.h"
#include "esp_adc/adc_cali.h"
#include "esp_adc/adc_cali_scheme.h"

static const char *TAG = "BATTERY_MONITOR";

/* 电池监控状态 */
static bool g_battery_initialized = false;
static adc_oneshot_unit_handle_t g_adc_handle = NULL;
static adc_cali_handle_t g_adc_cali_handle = NULL;

/* 电池电压参数 */
#define BATTERY_VOLTAGE_MAX     4200    // 满电电压 (mV)
#define BATTERY_VOLTAGE_MIN     3200    // 空电电压 (mV)
#define VOLTAGE_DIVIDER_RATIO   2.0f    // 分压比例

/**
 * @brief 电池监控初始化
 */
esp_err_t battery_monitor_init(void)
{
    if (g_battery_initialized) {
        ESP_LOGW(TAG, "电池监控已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化电池电压监控...");
    
    // 配置ADC
    adc_oneshot_unit_init_cfg_t init_config = {
        .unit_id = BAT_ADC_UNIT,
    };
    
    esp_err_t ret = adc_oneshot_new_unit(&init_config, &g_adc_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "ADC单元初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 配置ADC通道
    adc_oneshot_chan_cfg_t config = {
        .bitwidth = ADC_BITWIDTH_12,
        .atten = BAT_ADC_ATTEN,
    };
    
    ret = adc_oneshot_config_channel(g_adc_handle, BAT_ADC_CHANNEL, &config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "ADC通道配置失败: %s", esp_err_to_name(ret));
        adc_oneshot_del_unit(g_adc_handle);
        return ret;
    }
    
    // 初始化ADC校准
    adc_cali_curve_fitting_config_t cali_config = {
        .unit_id = BAT_ADC_UNIT,
        .chan = BAT_ADC_CHANNEL,
        .atten = BAT_ADC_ATTEN,
        .bitwidth = ADC_BITWIDTH_12,
    };
    
    ret = adc_cali_create_scheme_curve_fitting(&cali_config, &g_adc_cali_handle);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "ADC校准初始化失败，将使用原始值: %s", esp_err_to_name(ret));
        g_adc_cali_handle = NULL;
    }
    
    g_battery_initialized = true;
    ESP_LOGI(TAG, "电池电压监控初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 读取电池电压
 */
float battery_get_voltage(void)
{
    if (!g_battery_initialized) {
        ESP_LOGE(TAG, "电池监控未初始化");
        return 0.0f;
    }
    
    // 读取ADC原始值
    int adc_raw;
    esp_err_t ret = adc_oneshot_read(g_adc_handle, BAT_ADC_CHANNEL, &adc_raw);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "ADC读取失败: %s", esp_err_to_name(ret));
        return 0.0f;
    }
    
    // 转换为电压值
    int voltage_mv;
    if (g_adc_cali_handle) {
        ret = adc_cali_raw_to_voltage(g_adc_cali_handle, adc_raw, &voltage_mv);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "ADC校准转换失败: %s", esp_err_to_name(ret));
            return 0.0f;
        }
    } else {
        // 使用简单的线性转换
        voltage_mv = (adc_raw * 3300) / 4095;  // 假设参考电压为3.3V
    }
    
    // 考虑分压电路
    float battery_voltage = (float)voltage_mv * VOLTAGE_DIVIDER_RATIO / 1000.0f;
    
    ESP_LOGD(TAG, "电池电压: %.2fV (ADC原始值: %d, 转换值: %dmV)", 
             battery_voltage, adc_raw, voltage_mv);
    
    return battery_voltage;
}

/**
 * @brief 获取电池电量百分比
 */
uint8_t battery_get_percentage(void)
{
    float voltage = battery_get_voltage();
    
    if (voltage <= 0.0f) {
        return 0;
    }
    
    // 转换为毫伏
    int voltage_mv = (int)(voltage * 1000);
    
    // 限制电压范围
    if (voltage_mv >= BATTERY_VOLTAGE_MAX) {
        return 100;
    }
    
    if (voltage_mv <= BATTERY_VOLTAGE_MIN) {
        return 0;
    }
    
    // 线性计算电量百分比
    int percentage = ((voltage_mv - BATTERY_VOLTAGE_MIN) * 100) / 
                    (BATTERY_VOLTAGE_MAX - BATTERY_VOLTAGE_MIN);
    
    ESP_LOGD(TAG, "电池电量: %d%% (电压: %.2fV)", percentage, voltage);
    
    return (uint8_t)percentage;
}
