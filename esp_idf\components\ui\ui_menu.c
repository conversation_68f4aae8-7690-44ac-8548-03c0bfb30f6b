/**
 * @file ui_menu.c
 * @brief TIMO主菜单页面实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "ui_menu.h"
#include "ui_theme.h"
#include "hardware_config.h"
#include "esp_log.h"

static const char *TAG = "UI_MENU";

static lv_obj_t *g_menu_page = NULL;

lv_obj_t* ui_menu_create(void)
{
    ESP_LOGI(TAG, "创建主菜单页面...");
    
    g_menu_page = lv_obj_create(NULL);
    lv_obj_set_size(g_menu_page, LCD_WIDTH, LCD_HEIGHT);
    lv_obj_set_style_bg_color(g_menu_page, lv_color_hex(0x1E1E1E), 0);
    
    // 创建菜单标题
    lv_obj_t *title = lv_label_create(g_menu_page);
    lv_label_set_text(title, "主菜单");
    lv_obj_add_style(title, ui_theme_create_label_style(18), 0);
    lv_obj_center(title);
    
    ESP_LOGI(TAG, "主菜单页面创建完成");
    return g_menu_page;
}
