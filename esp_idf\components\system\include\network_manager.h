/**
 * @file network_manager.h
 * @brief TIMO网络管理器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef NETWORK_MANAGER_H
#define NETWORK_MANAGER_H

#include "esp_err.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

esp_err_t network_manager_init(void);
esp_err_t network_manager_start_wifi(void);
esp_err_t network_manager_start_bluetooth(void);
esp_err_t network_manager_stop(void);
bool network_manager_is_wifi_connected(void);
bool network_manager_is_bluetooth_connected(void);
int8_t network_manager_get_wifi_rssi(void);

#ifdef __cplusplus
}
#endif

#endif /* NETWORK_MANAGER_H */
