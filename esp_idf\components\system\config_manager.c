/**
 * @file config_manager.c
 * @brief TIMO配置管理器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "config_manager.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "nvs.h"

static const char *TAG = "CONFIG_MANAGER";
static const char *NVS_NAMESPACE = "timo_config";

/**
 * @brief 初始化配置管理器
 */
esp_err_t config_manager_init(void)
{
    ESP_LOGI(TAG, "初始化配置管理器...");
    
    // NVS已在main中初始化
    ESP_LOGI(TAG, "配置管理器初始化完成");
    return ESP_OK;
}

/**
 * @brief 加载系统配置
 */
esp_err_t config_manager_load_system_config(system_config_t *config)
{
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }
    
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open(NVS_NAMESPACE, NVS_READONLY, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "打开NVS失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    size_t required_size = sizeof(system_config_t);
    ret = nvs_get_blob(nvs_handle, "system_config", config, &required_size);
    
    nvs_close(nvs_handle);
    
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "加载系统配置失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "系统配置加载成功");
    return ESP_OK;
}

/**
 * @brief 保存系统配置
 */
esp_err_t config_manager_save_system_config(const system_config_t *config)
{
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }
    
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "打开NVS失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ret = nvs_set_blob(nvs_handle, "system_config", config, sizeof(system_config_t));
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "保存系统配置失败: %s", esp_err_to_name(ret));
        nvs_close(nvs_handle);
        return ret;
    }
    
    ret = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "提交NVS失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "系统配置保存成功");
    return ESP_OK;
}

/**
 * @brief 重置配置为默认值
 */
esp_err_t config_manager_reset_to_default(void)
{
    ESP_LOGI(TAG, "重置配置为默认值...");
    
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "打开NVS失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ret = nvs_erase_all(nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "清除NVS失败: %s", esp_err_to_name(ret));
        nvs_close(nvs_handle);
        return ret;
    }
    
    ret = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    
    ESP_LOGI(TAG, "配置重置完成");
    return ret;
}
