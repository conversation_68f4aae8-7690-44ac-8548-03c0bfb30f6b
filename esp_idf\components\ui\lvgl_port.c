/**
 * @file lvgl_port.c
 * @brief LVGL移植层实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "lvgl_port.h"
#include "hardware_hal.h"
#include "hardware_config.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"

static const char *TAG = "LVGL_PORT";

/* LVGL对象 */
static lv_disp_t *g_display = NULL;
static lv_indev_t *g_touch_input = NULL;
static bool g_lvgl_initialized = false;

/* 显示缓冲区 */
static lv_color_t *g_disp_buf1 = NULL;
static lv_color_t *g_disp_buf2 = NULL;

/* 互斥锁 */
static SemaphoreHandle_t g_lvgl_mutex = NULL;

/**
 * @brief LVGL显示刷新回调
 */
static void lvgl_disp_flush_cb(lv_disp_drv_t *drv, const lv_area_t *area, lv_color_t *color_map)
{
    int32_t x1 = area->x1;
    int32_t y1 = area->y1;
    int32_t x2 = area->x2;
    int32_t y2 = area->y2;
    
    // 计算区域大小
    int32_t width = x2 - x1 + 1;
    int32_t height = y2 - y1 + 1;
    
    // 发送数据到LCD
    esp_err_t ret = lcd_draw_bitmap(x1, y1, width, height, (uint16_t*)color_map);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LCD绘制失败");
    }
    
    // 通知LVGL刷新完成
    lv_disp_flush_ready(drv);
}

/**
 * @brief LVGL触摸读取回调
 */
static void lvgl_touch_read_cb(lv_indev_drv_t *drv, lv_indev_data_t *data)
{
    touch_point_t point;
    esp_err_t ret = touch_read_point(&point);
    
    if (ret == ESP_OK && point.pressed) {
        data->state = LV_INDEV_STATE_PRESSED;
        data->point.x = point.x;
        data->point.y = point.y;
        
        ESP_LOGD(TAG, "触摸点: (%d, %d)", point.x, point.y);
    } else {
        data->state = LV_INDEV_STATE_RELEASED;
    }
}

/**
 * @brief LVGL时钟回调
 */
static uint32_t lvgl_tick_get_cb(void)
{
    return esp_timer_get_time() / 1000;
}

/**
 * @brief LVGL互斥锁获取
 */
static void lvgl_lock(void)
{
    if (g_lvgl_mutex) {
        xSemaphoreTake(g_lvgl_mutex, portMAX_DELAY);
    }
}

/**
 * @brief LVGL互斥锁释放
 */
static void lvgl_unlock(void)
{
    if (g_lvgl_mutex) {
        xSemaphoreGive(g_lvgl_mutex);
    }
}

/**
 * @brief 初始化LVGL移植层
 */
esp_err_t lvgl_port_init(void)
{
    if (g_lvgl_initialized) {
        ESP_LOGW(TAG, "LVGL移植层已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化LVGL移植层...");
    
    // 创建互斥锁
    g_lvgl_mutex = xSemaphoreCreateMutex();
    if (!g_lvgl_mutex) {
        ESP_LOGE(TAG, "创建LVGL互斥锁失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 初始化LVGL
    lv_init();
    
    // 设置时钟回调
    lv_tick_set_cb(lvgl_tick_get_cb);
    
    // 分配显示缓冲区
    size_t buf_size = LCD_WIDTH * LCD_HEIGHT / 10;  // 1/10屏幕大小
    g_disp_buf1 = heap_caps_malloc(buf_size * sizeof(lv_color_t), MALLOC_CAP_DMA);
    g_disp_buf2 = heap_caps_malloc(buf_size * sizeof(lv_color_t), MALLOC_CAP_DMA);
    
    if (!g_disp_buf1 || !g_disp_buf2) {
        ESP_LOGE(TAG, "分配显示缓冲区失败");
        return ESP_ERR_NO_MEM;
    }
    
    ESP_LOGI(TAG, "显示缓冲区大小: %d bytes x2", buf_size * sizeof(lv_color_t));
    
    // 初始化显示缓冲区
    static lv_disp_draw_buf_t disp_buf;
    lv_disp_draw_buf_init(&disp_buf, g_disp_buf1, g_disp_buf2, buf_size);
    
    // 注册显示驱动
    static lv_disp_drv_t disp_drv;
    lv_disp_drv_init(&disp_drv);
    disp_drv.draw_buf = &disp_buf;
    disp_drv.flush_cb = lvgl_disp_flush_cb;
    disp_drv.hor_res = LCD_WIDTH;
    disp_drv.ver_res = LCD_HEIGHT;
    disp_drv.antialiasing = 1;
    
    g_display = lv_disp_drv_register(&disp_drv);
    if (!g_display) {
        ESP_LOGE(TAG, "注册显示驱动失败");
        return ESP_ERR_INVALID_STATE;
    }
    
    // 注册触摸输入驱动
    static lv_indev_drv_t touch_drv;
    lv_indev_drv_init(&touch_drv);
    touch_drv.type = LV_INDEV_TYPE_POINTER;
    touch_drv.read_cb = lvgl_touch_read_cb;
    
    g_touch_input = lv_indev_drv_register(&touch_drv);
    if (!g_touch_input) {
        ESP_LOGE(TAG, "注册触摸驱动失败");
        return ESP_ERR_INVALID_STATE;
    }
    
    // 设置互斥锁回调
    lv_lock_set_cb(lvgl_lock, lvgl_unlock);
    
    g_lvgl_initialized = true;
    ESP_LOGI(TAG, "LVGL移植层初始化完成");
    ESP_LOGI(TAG, "显示分辨率: %dx%d", LCD_WIDTH, LCD_HEIGHT);
    
    return ESP_OK;
}

/**
 * @brief 反初始化LVGL移植层
 */
esp_err_t lvgl_port_deinit(void)
{
    if (!g_lvgl_initialized) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "反初始化LVGL移植层...");
    
    // 反初始化LVGL
    lv_deinit();
    
    // 释放显示缓冲区
    if (g_disp_buf1) {
        free(g_disp_buf1);
        g_disp_buf1 = NULL;
    }
    
    if (g_disp_buf2) {
        free(g_disp_buf2);
        g_disp_buf2 = NULL;
    }
    
    // 删除互斥锁
    if (g_lvgl_mutex) {
        vSemaphoreDelete(g_lvgl_mutex);
        g_lvgl_mutex = NULL;
    }
    
    g_display = NULL;
    g_touch_input = NULL;
    g_lvgl_initialized = false;
    
    ESP_LOGI(TAG, "LVGL移植层反初始化完成");
    return ESP_OK;
}

/**
 * @brief 获取显示器对象
 */
lv_disp_t* lvgl_port_get_display(void)
{
    return g_display;
}

/**
 * @brief 获取触摸输入设备对象
 */
lv_indev_t* lvgl_port_get_touch_input(void)
{
    return g_touch_input;
}
