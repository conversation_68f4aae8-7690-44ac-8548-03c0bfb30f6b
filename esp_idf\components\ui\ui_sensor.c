/**
 * @file ui_sensor.c
 * @brief TIMO传感器页面实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "ui_sensor.h"
#include "ui_theme.h"
#include "hardware_config.h"
#include "esp_log.h"

static const char *TAG = "UI_SENSOR";
static lv_obj_t *g_sensor_page = NULL;
static lv_obj_t *g_temp_value = NULL;
static lv_obj_t *g_humidity_value = NULL;
static lv_obj_t *g_lux_value = NULL;
static lv_obj_t *g_co2_value = NULL;

lv_obj_t* ui_sensor_create(void)
{
    ESP_LOGI(TAG, "创建传感器页面...");
    
    g_sensor_page = lv_obj_create(NULL);
    lv_obj_set_size(g_sensor_page, LCD_WIDTH, LCD_HEIGHT);
    lv_obj_set_style_bg_color(g_sensor_page, lv_color_hex(0x0E1E2E), 0);
    
    // 创建传感器数据显示
    g_temp_value = lv_label_create(g_sensor_page);
    lv_label_set_text(g_temp_value, "温度: 25.0°C");
    lv_obj_add_style(g_temp_value, ui_theme_create_label_style(16), 0);
    lv_obj_set_pos(g_temp_value, 50, 100);
    
    g_humidity_value = lv_label_create(g_sensor_page);
    lv_label_set_text(g_humidity_value, "湿度: 60%");
    lv_obj_add_style(g_humidity_value, ui_theme_create_label_style(16), 0);
    lv_obj_set_pos(g_humidity_value, 50, 150);
    
    g_lux_value = lv_label_create(g_sensor_page);
    lv_label_set_text(g_lux_value, "光照: 500 lux");
    lv_obj_add_style(g_lux_value, ui_theme_create_label_style(16), 0);
    lv_obj_set_pos(g_lux_value, 50, 200);
    
    g_co2_value = lv_label_create(g_sensor_page);
    lv_label_set_text(g_co2_value, "CO2: 400 ppm");
    lv_obj_add_style(g_co2_value, ui_theme_create_label_style(16), 0);
    lv_obj_set_pos(g_co2_value, 50, 250);
    
    ESP_LOGI(TAG, "传感器页面创建完成");
    return g_sensor_page;
}

esp_err_t ui_sensor_update_data(float temperature, float humidity, float lux, uint16_t co2)
{
    if (!g_temp_value || !g_humidity_value || !g_lux_value || !g_co2_value) {
        return ESP_ERR_INVALID_STATE;
    }
    
    char temp_str[32];
    snprintf(temp_str, sizeof(temp_str), "温度: %.1f°C", temperature);
    lv_label_set_text(g_temp_value, temp_str);
    
    char humidity_str[32];
    snprintf(humidity_str, sizeof(humidity_str), "湿度: %.0f%%", humidity);
    lv_label_set_text(g_humidity_value, humidity_str);
    
    char lux_str[32];
    snprintf(lux_str, sizeof(lux_str), "光照: %.0f lux", lux);
    lv_label_set_text(g_lux_value, lux_str);
    
    char co2_str[32];
    snprintf(co2_str, sizeof(co2_str), "CO2: %d ppm", co2);
    lv_label_set_text(g_co2_value, co2_str);
    
    return ESP_OK;
}
