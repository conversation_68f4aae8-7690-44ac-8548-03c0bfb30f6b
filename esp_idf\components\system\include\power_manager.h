/**
 * @file power_manager.h
 * @brief TIMO电源管理器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef POWER_MANAGER_H
#define POWER_MANAGER_H

#include "esp_err.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

esp_err_t power_manager_init(void);
esp_err_t power_manager_start(void);
esp_err_t power_manager_stop(void);
void power_manager_update_status(void);
uint8_t power_manager_get_battery_level(void);
bool power_manager_is_charging(void);

#ifdef __cplusplus
}
#endif

#endif /* POWER_MANAGER_H */
