/**
 * @file sensor_drivers.c
 * @brief 传感器驱动程序集合
 * @version 1.0.0
 * @date 2025-06-27
 * 
 * 包含传感器：
 * - AHT30: 温湿度传感器
 * - BH1750: 光照传感器  
 * - SGP30: CO2/TVOC传感器
 * - QMI8658: 姿态传感器
 */

#include "sensor_drivers.h"
#include "hardware_hal.h"
#include "i2c_bus.h"
#include "hardware_config.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <math.h>

static const char *TAG = "SENSORS";

/* 传感器初始化状态 */
static bool g_sensors_initialized = false;
static bool g_aht30_ready = false;
static bool g_bh1750_ready = false;
static bool g_sgp30_ready = false;
static bool g_qmi8658_ready = false;

/* ========== AHT30 温湿度传感器 ========== */

#define AHT30_CMD_INIT          0xBE
#define AHT30_CMD_TRIGGER       0xAC
#define AHT30_CMD_SOFT_RESET    0xBA
#define AHT30_STATUS_BUSY       0x80
#define AHT30_STATUS_CALIBRATED 0x08

/**
 * @brief AHT30初始化
 */
static esp_err_t aht30_init(void)
{
    ESP_LOGI(TAG, "初始化AHT30温湿度传感器...");
    
    if (!i2c_bus_device_online(AHT30_I2C_ADDR)) {
        ESP_LOGE(TAG, "AHT30设备未找到");
        return ESP_ERR_NOT_FOUND;
    }
    
    // 软复位
    uint8_t reset_cmd = AHT30_CMD_SOFT_RESET;
    esp_err_t ret = i2c_bus_write_bytes(AHT30_I2C_ADDR, 0, &reset_cmd, 1);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "AHT30软复位失败");
        return ret;
    }
    
    vTaskDelay(pdMS_TO_TICKS(20));
    
    // 初始化命令
    uint8_t init_cmd[] = {AHT30_CMD_INIT, 0x08, 0x00};
    ret = i2c_bus_write_bytes(AHT30_I2C_ADDR, 0, init_cmd, sizeof(init_cmd));
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "AHT30初始化命令失败");
        return ret;
    }
    
    vTaskDelay(pdMS_TO_TICKS(10));
    
    // 检查状态
    uint8_t status;
    ret = i2c_bus_read_byte(AHT30_I2C_ADDR, 0, &status);
    if (ret != ESP_OK || !(status & AHT30_STATUS_CALIBRATED)) {
        ESP_LOGE(TAG, "AHT30校准失败，状态: 0x%02X", status);
        return ESP_ERR_INVALID_RESPONSE;
    }
    
    g_aht30_ready = true;
    ESP_LOGI(TAG, "AHT30初始化完成");
    return ESP_OK;
}

/**
 * @brief 读取AHT30数据
 */
esp_err_t aht30_read(aht30_data_t *data)
{
    if (!g_aht30_ready || !data) {
        return ESP_ERR_INVALID_STATE;
    }
    
    // 触发测量
    uint8_t trigger_cmd[] = {AHT30_CMD_TRIGGER, 0x33, 0x00};
    esp_err_t ret = i2c_bus_write_bytes(AHT30_I2C_ADDR, 0, trigger_cmd, sizeof(trigger_cmd));
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 等待测量完成
    vTaskDelay(pdMS_TO_TICKS(80));
    
    // 读取数据
    uint8_t raw_data[6];
    ret = i2c_bus_read_bytes(AHT30_I2C_ADDR, 0, raw_data, sizeof(raw_data));
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 检查忙状态
    if (raw_data[0] & AHT30_STATUS_BUSY) {
        ESP_LOGW(TAG, "AHT30仍在测量中");
        return ESP_ERR_TIMEOUT;
    }
    
    // 解析数据
    uint32_t humidity_raw = ((uint32_t)raw_data[1] << 12) | 
                           ((uint32_t)raw_data[2] << 4) | 
                           (raw_data[3] >> 4);
    
    uint32_t temperature_raw = (((uint32_t)raw_data[3] & 0x0F) << 16) | 
                              ((uint32_t)raw_data[4] << 8) | 
                              raw_data[5];
    
    // 转换为实际值
    data->humidity = (float)humidity_raw / 1048576.0f * 100.0f;
    data->temperature = (float)temperature_raw / 1048576.0f * 200.0f - 50.0f;
    
    ESP_LOGD(TAG, "AHT30: 温度=%.1f°C, 湿度=%.1f%%", data->temperature, data->humidity);
    
    return ESP_OK;
}

/* ========== BH1750 光照传感器 ========== */

#define BH1750_CMD_POWER_ON     0x01
#define BH1750_CMD_RESET        0x07
#define BH1750_CMD_CONT_H_RES   0x10    // 连续高分辨率模式

/**
 * @brief BH1750初始化
 */
static esp_err_t bh1750_init(void)
{
    ESP_LOGI(TAG, "初始化BH1750光照传感器...");
    
    if (!i2c_bus_device_online(BH1750_I2C_ADDR)) {
        ESP_LOGE(TAG, "BH1750设备未找到");
        return ESP_ERR_NOT_FOUND;
    }
    
    // 上电
    uint8_t power_on = BH1750_CMD_POWER_ON;
    esp_err_t ret = i2c_bus_write_bytes(BH1750_I2C_ADDR, 0, &power_on, 1);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "BH1750上电失败");
        return ret;
    }
    
    vTaskDelay(pdMS_TO_TICKS(10));
    
    // 复位
    uint8_t reset = BH1750_CMD_RESET;
    ret = i2c_bus_write_bytes(BH1750_I2C_ADDR, 0, &reset, 1);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "BH1750复位失败");
        return ret;
    }
    
    vTaskDelay(pdMS_TO_TICKS(10));
    
    // 设置连续高分辨率模式
    uint8_t mode = BH1750_CMD_CONT_H_RES;
    ret = i2c_bus_write_bytes(BH1750_I2C_ADDR, 0, &mode, 1);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "BH1750设置模式失败");
        return ret;
    }
    
    g_bh1750_ready = true;
    ESP_LOGI(TAG, "BH1750初始化完成");
    return ESP_OK;
}

/**
 * @brief 读取BH1750数据
 */
esp_err_t bh1750_read(bh1750_data_t *data)
{
    if (!g_bh1750_ready || !data) {
        return ESP_ERR_INVALID_STATE;
    }
    
    // 读取2字节数据
    uint8_t raw_data[2];
    esp_err_t ret = i2c_bus_read_bytes(BH1750_I2C_ADDR, 0, raw_data, sizeof(raw_data));
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 转换为光照强度 (lux)
    uint16_t raw_value = (raw_data[0] << 8) | raw_data[1];
    data->lux = (float)raw_value / 1.2f;  // 分辨率为1.2 lux
    
    ESP_LOGD(TAG, "BH1750: 光照强度=%.1f lux", data->lux);
    
    return ESP_OK;
}

/* ========== SGP30 CO2传感器 ========== */

#define SGP30_CMD_INIT_AIR_QUALITY  0x2003
#define SGP30_CMD_MEASURE_AIR       0x2008
#define SGP30_CMD_GET_BASELINE      0x2015
#define SGP30_CMD_SET_BASELINE      0x201E
#define SGP30_CMD_SET_HUMIDITY      0x2061

/**
 * @brief SGP30初始化
 */
static esp_err_t sgp30_init(void)
{
    ESP_LOGI(TAG, "初始化SGP30 CO2传感器...");
    
    if (!i2c_bus_device_online(SGP30_I2C_ADDR)) {
        ESP_LOGE(TAG, "SGP30设备未找到");
        return ESP_ERR_NOT_FOUND;
    }
    
    // 初始化空气质量测量
    uint8_t init_cmd[] = {0x20, 0x03};
    esp_err_t ret = i2c_bus_write_bytes(SGP30_I2C_ADDR, 0, init_cmd, sizeof(init_cmd));
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "SGP30初始化失败");
        return ret;
    }
    
    // 等待初始化完成
    vTaskDelay(pdMS_TO_TICKS(10));
    
    g_sgp30_ready = true;
    ESP_LOGI(TAG, "SGP30初始化完成");
    ESP_LOGW(TAG, "SGP30需要15秒预热时间才能获得准确读数");
    
    return ESP_OK;
}

/**
 * @brief 读取SGP30数据
 */
esp_err_t sgp30_read(sgp30_data_t *data)
{
    if (!g_sgp30_ready || !data) {
        return ESP_ERR_INVALID_STATE;
    }
    
    // 发送测量命令
    uint8_t measure_cmd[] = {0x20, 0x08};
    esp_err_t ret = i2c_bus_write_bytes(SGP30_I2C_ADDR, 0, measure_cmd, sizeof(measure_cmd));
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 等待测量完成
    vTaskDelay(pdMS_TO_TICKS(12));
    
    // 读取6字节数据 (CO2 + CRC + TVOC + CRC)
    uint8_t raw_data[6];
    ret = i2c_bus_read_bytes(SGP30_I2C_ADDR, 0, raw_data, sizeof(raw_data));
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 解析数据 (忽略CRC校验)
    data->co2 = (raw_data[0] << 8) | raw_data[1];
    data->tvoc = (raw_data[3] << 8) | raw_data[4];
    
    ESP_LOGD(TAG, "SGP30: CO2=%d ppm, TVOC=%d ppb", data->co2, data->tvoc);
    
    return ESP_OK;
}

/* ========== QMI8658 姿态传感器 ========== */

#define QMI8658_REG_WHO_AM_I    0x00
#define QMI8658_REG_CTRL1       0x02
#define QMI8658_REG_CTRL2       0x03
#define QMI8658_REG_CTRL3       0x04
#define QMI8658_REG_CTRL7       0x08
#define QMI8658_REG_AX_L        0x35
#define QMI8658_REG_GX_L        0x3B

#define QMI8658_WHO_AM_I_VALUE  0x05

/**
 * @brief QMI8658初始化
 */
static esp_err_t qmi8658_init(void)
{
    ESP_LOGI(TAG, "初始化QMI8658姿态传感器...");
    
    if (!i2c_bus_device_online(QMI8658_I2C_ADDR)) {
        ESP_LOGE(TAG, "QMI8658设备未找到");
        return ESP_ERR_NOT_FOUND;
    }
    
    // 检查设备ID
    uint8_t who_am_i;
    esp_err_t ret = i2c_bus_read_byte(QMI8658_I2C_ADDR, QMI8658_REG_WHO_AM_I, &who_am_i);
    if (ret != ESP_OK || who_am_i != QMI8658_WHO_AM_I_VALUE) {
        ESP_LOGE(TAG, "QMI8658设备ID错误: 0x%02X", who_am_i);
        return ESP_ERR_INVALID_RESPONSE;
    }
    
    // 配置加速度计: ±4g, 250Hz
    ret = i2c_bus_write_byte(QMI8658_I2C_ADDR, QMI8658_REG_CTRL2, 0x24);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "QMI8658配置加速度计失败");
        return ret;
    }
    
    // 配置陀螺仪: ±512dps, 250Hz
    ret = i2c_bus_write_byte(QMI8658_I2C_ADDR, QMI8658_REG_CTRL3, 0x54);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "QMI8658配置陀螺仪失败");
        return ret;
    }
    
    // 使能加速度计和陀螺仪
    ret = i2c_bus_write_byte(QMI8658_I2C_ADDR, QMI8658_REG_CTRL7, 0x03);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "QMI8658使能传感器失败");
        return ret;
    }
    
    vTaskDelay(pdMS_TO_TICKS(10));
    
    g_qmi8658_ready = true;
    ESP_LOGI(TAG, "QMI8658初始化完成");
    return ESP_OK;
}

/**
 * @brief 读取QMI8658数据
 */
esp_err_t qmi8658_read(qmi8658_data_t *data)
{
    if (!g_qmi8658_ready || !data) {
        return ESP_ERR_INVALID_STATE;
    }
    
    // 读取加速度数据 (6字节)
    uint8_t acc_data[6];
    esp_err_t ret = i2c_bus_read_bytes(QMI8658_I2C_ADDR, QMI8658_REG_AX_L, acc_data, sizeof(acc_data));
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 读取陀螺仪数据 (6字节)
    uint8_t gyro_data[6];
    ret = i2c_bus_read_bytes(QMI8658_I2C_ADDR, QMI8658_REG_GX_L, gyro_data, sizeof(gyro_data));
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 转换加速度数据 (±4g)
    int16_t acc_x_raw = (int16_t)((acc_data[1] << 8) | acc_data[0]);
    int16_t acc_y_raw = (int16_t)((acc_data[3] << 8) | acc_data[2]);
    int16_t acc_z_raw = (int16_t)((acc_data[5] << 8) | acc_data[4]);
    
    data->acc_x = (float)acc_x_raw / 8192.0f;  // 4g / 32768 * 2
    data->acc_y = (float)acc_y_raw / 8192.0f;
    data->acc_z = (float)acc_z_raw / 8192.0f;
    
    // 转换陀螺仪数据 (±512dps)
    int16_t gyro_x_raw = (int16_t)((gyro_data[1] << 8) | gyro_data[0]);
    int16_t gyro_y_raw = (int16_t)((gyro_data[3] << 8) | gyro_data[2]);
    int16_t gyro_z_raw = (int16_t)((gyro_data[5] << 8) | gyro_data[4]);
    
    data->gyro_x = (float)gyro_x_raw / 64.0f;  // 512dps / 32768 * 2
    data->gyro_y = (float)gyro_y_raw / 64.0f;
    data->gyro_z = (float)gyro_z_raw / 64.0f;
    
    ESP_LOGD(TAG, "QMI8658: ACC(%.2f,%.2f,%.2f)g GYRO(%.1f,%.1f,%.1f)dps", 
             data->acc_x, data->acc_y, data->acc_z,
             data->gyro_x, data->gyro_y, data->gyro_z);
    
    return ESP_OK;
}

/* ========== 传感器统一接口 ========== */

/**
 * @brief 初始化所有传感器
 */
esp_err_t sensors_init(void)
{
    if (g_sensors_initialized) {
        ESP_LOGW(TAG, "传感器已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化所有传感器...");
    
    esp_err_t ret;
    int success_count = 0;
    
    // 初始化AHT30
    ret = aht30_init();
    if (ret == ESP_OK) {
        success_count++;
    }
    
    // 初始化BH1750
    ret = bh1750_init();
    if (ret == ESP_OK) {
        success_count++;
    }
    
    // 初始化SGP30
    ret = sgp30_init();
    if (ret == ESP_OK) {
        success_count++;
    }
    
    // 初始化QMI8658
    ret = qmi8658_init();
    if (ret == ESP_OK) {
        success_count++;
    }
    
    g_sensors_initialized = true;
    ESP_LOGI(TAG, "传感器初始化完成，成功: %d/4", success_count);
    
    return (success_count > 0) ? ESP_OK : ESP_ERR_NOT_FOUND;
}
