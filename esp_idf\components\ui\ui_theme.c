/**
 * @file ui_theme.c
 * @brief TIMO UI主题系统实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "ui_theme.h"
#include "esp_log.h"

static const char *TAG = "UI_THEME";

/* 当前主题 */
static ui_theme_id_t g_current_theme = UI_THEME_DEFAULT;
static lv_theme_t *g_lvgl_theme = NULL;

/* 主题定义 */
static const ui_theme_info_t g_themes[UI_THEME_MAX] = {
    {
        .id = UI_THEME_DEFAULT,
        .name = "默认",
        .description = "经典白色主题",
        .colors = {
            .primary = LV_COLOR_MAKE(0x01, 0x95, 0xF7),
            .secondary = LV_COLOR_MAKE(0x6C, 0x75, 0x7D),
            .background = LV_COLOR_MAKE(0xFF, 0xFF, 0xFF),
            .surface = LV_COLOR_MAKE(0xF8, 0xF9, 0xFA),
            .text_primary = LV_COLOR_MAKE(0x21, 0x25, 0x29),
            .text_secondary = LV_COLOR_MAKE(0x6C, 0x75, 0x7D),
            .accent = LV_COLOR_MAKE(0xFD, 0x7E, 0x14),
            .error = LV_COLOR_MAKE(0xDC, 0x35, 0x45),
            .warning = LV_COLOR_MAKE(0xFF, 0xC1, 0x07),
            .success = LV_COLOR_MAKE(0x19, 0x8F, 0x54)
        },
        .is_dark = false
    },
    {
        .id = UI_THEME_DARK,
        .name = "深色",
        .description = "护眼深色主题",
        .colors = {
            .primary = LV_COLOR_MAKE(0x0D, 0x6E, 0xFD),
            .secondary = LV_COLOR_MAKE(0x6C, 0x75, 0x7D),
            .background = LV_COLOR_MAKE(0x21, 0x25, 0x29),
            .surface = LV_COLOR_MAKE(0x34, 0x3A, 0x40),
            .text_primary = LV_COLOR_MAKE(0xFF, 0xFF, 0xFF),
            .text_secondary = LV_COLOR_MAKE(0xAD, 0xB5, 0xBD),
            .accent = LV_COLOR_MAKE(0xFD, 0x7E, 0x14),
            .error = LV_COLOR_MAKE(0xF5, 0x36, 0x5C),
            .warning = LV_COLOR_MAKE(0xFF, 0xC1, 0x07),
            .success = LV_COLOR_MAKE(0x20, 0xC9, 0x97)
        },
        .is_dark = true
    },
    {
        .id = UI_THEME_BLUE,
        .name = "海洋蓝",
        .description = "清新蓝色主题",
        .colors = {
            .primary = LV_COLOR_MAKE(0x00, 0x7B, 0xFF),
            .secondary = LV_COLOR_MAKE(0x4A, 0x90, 0xE2),
            .background = LV_COLOR_MAKE(0xF0, 0xF8, 0xFF),
            .surface = LV_COLOR_MAKE(0xE3, 0xF2, 0xFD),
            .text_primary = LV_COLOR_MAKE(0x1A, 0x1A, 0x1A),
            .text_secondary = LV_COLOR_MAKE(0x5A, 0x5A, 0x5A),
            .accent = LV_COLOR_MAKE(0xFF, 0x6B, 0x35),
            .error = LV_COLOR_MAKE(0xFF, 0x3B, 0x30),
            .warning = LV_COLOR_MAKE(0xFF, 0xCC, 0x02),
            .success = LV_COLOR_MAKE(0x34, 0xC7, 0x59)
        },
        .is_dark = false
    },
    {
        .id = UI_THEME_GREEN,
        .name = "自然绿",
        .description = "清新绿色主题",
        .colors = {
            .primary = LV_COLOR_MAKE(0x28, 0xA7, 0x45),
            .secondary = LV_COLOR_MAKE(0x6F, 0xB3, 0x7C),
            .background = LV_COLOR_MAKE(0xF0, 0xFF, 0xF0),
            .surface = LV_COLOR_MAKE(0xE8, 0xF5, 0xE8),
            .text_primary = LV_COLOR_MAKE(0x1A, 0x1A, 0x1A),
            .text_secondary = LV_COLOR_MAKE(0x5A, 0x5A, 0x5A),
            .accent = LV_COLOR_MAKE(0xFF, 0x9F, 0x00),
            .error = LV_COLOR_MAKE(0xFF, 0x3B, 0x30),
            .warning = LV_COLOR_MAKE(0xFF, 0xCC, 0x02),
            .success = LV_COLOR_MAKE(0x30, 0xD1, 0x58)
        },
        .is_dark = false
    },
    {
        .id = UI_THEME_ORANGE,
        .name = "活力橙",
        .description = "温暖橙色主题",
        .colors = {
            .primary = LV_COLOR_MAKE(0xFF, 0x6D, 0x00),
            .secondary = LV_COLOR_MAKE(0xFF, 0xA5, 0x00),
            .background = LV_COLOR_MAKE(0xFF, 0xF8, 0xF0),
            .surface = LV_COLOR_MAKE(0xFF, 0xF0, 0xE6),
            .text_primary = LV_COLOR_MAKE(0x1A, 0x1A, 0x1A),
            .text_secondary = LV_COLOR_MAKE(0x5A, 0x5A, 0x5A),
            .accent = LV_COLOR_MAKE(0x00, 0x7A, 0xFF),
            .error = LV_COLOR_MAKE(0xFF, 0x3B, 0x30),
            .warning = LV_COLOR_MAKE(0xFF, 0xCC, 0x02),
            .success = LV_COLOR_MAKE(0x34, 0xC7, 0x59)
        },
        .is_dark = false
    },
    {
        .id = UI_THEME_PURPLE,
        .name = "神秘紫",
        .description = "优雅紫色主题",
        .colors = {
            .primary = LV_COLOR_MAKE(0x7C, 0x3A, 0xED),
            .secondary = LV_COLOR_MAKE(0xA8, 0x5C, 0xF9),
            .background = LV_COLOR_MAKE(0xF8, 0xF0, 0xFF),
            .surface = LV_COLOR_MAKE(0xF0, 0xE6, 0xFF),
            .text_primary = LV_COLOR_MAKE(0x1A, 0x1A, 0x1A),
            .text_secondary = LV_COLOR_MAKE(0x5A, 0x5A, 0x5A),
            .accent = LV_COLOR_MAKE(0xFF, 0x6B, 0x35),
            .error = LV_COLOR_MAKE(0xFF, 0x3B, 0x30),
            .warning = LV_COLOR_MAKE(0xFF, 0xCC, 0x02),
            .success = LV_COLOR_MAKE(0x34, 0xC7, 0x59)
        },
        .is_dark = false
    }
};

/**
 * @brief 初始化UI主题系统
 */
esp_err_t ui_theme_init(void)
{
    ESP_LOGI(TAG, "初始化UI主题系统...");
    
    // 应用默认主题
    esp_err_t ret = ui_theme_apply(UI_THEME_DEFAULT);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "应用默认主题失败");
        return ret;
    }
    
    ESP_LOGI(TAG, "UI主题系统初始化完成，共 %d 个主题", UI_THEME_MAX);
    return ESP_OK;
}

/**
 * @brief 应用指定主题
 */
esp_err_t ui_theme_apply(ui_theme_id_t theme_id)
{
    if (theme_id >= UI_THEME_MAX) {
        ESP_LOGE(TAG, "无效的主题ID: %d", theme_id);
        return ESP_ERR_INVALID_ARG;
    }
    
    const ui_theme_info_t *theme_info = &g_themes[theme_id];
    ESP_LOGI(TAG, "应用主题: %s (%s)", theme_info->name, theme_info->description);
    
    // 创建LVGL主题
    g_lvgl_theme = lv_theme_default_init(
        lv_disp_get_default(),
        theme_info->colors.primary,
        theme_info->colors.secondary,
        theme_info->is_dark,
        LV_FONT_DEFAULT
    );
    
    if (!g_lvgl_theme) {
        ESP_LOGE(TAG, "创建LVGL主题失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 应用主题到显示器
    lv_disp_set_theme(lv_disp_get_default(), g_lvgl_theme);
    
    g_current_theme = theme_id;
    ESP_LOGI(TAG, "主题应用成功: %s", theme_info->name);
    
    return ESP_OK;
}

/**
 * @brief 获取当前主题ID
 */
ui_theme_id_t ui_theme_get_current(void)
{
    return g_current_theme;
}

/**
 * @brief 获取主题信息
 */
const ui_theme_info_t* ui_theme_get_info(ui_theme_id_t theme_id)
{
    if (theme_id >= UI_THEME_MAX) {
        return NULL;
    }
    
    return &g_themes[theme_id];
}

/**
 * @brief 获取主题数量
 */
uint8_t ui_theme_get_count(void)
{
    return UI_THEME_MAX;
}

/**
 * @brief 获取主题颜色
 */
const ui_theme_colors_t* ui_theme_get_colors(ui_theme_id_t theme_id)
{
    if (theme_id >= UI_THEME_MAX) {
        return NULL;
    }
    
    return &g_themes[theme_id].colors;
}

/**
 * @brief 创建圆形样式
 */
lv_style_t* ui_theme_create_circle_style(lv_coord_t radius)
{
    static lv_style_t style;
    lv_style_init(&style);
    
    const ui_theme_colors_t *colors = ui_theme_get_colors(g_current_theme);
    
    lv_style_set_radius(&style, radius);
    lv_style_set_bg_color(&style, colors->surface);
    lv_style_set_bg_opa(&style, LV_OPA_COVER);
    lv_style_set_border_width(&style, 2);
    lv_style_set_border_color(&style, colors->primary);
    lv_style_set_shadow_width(&style, 10);
    lv_style_set_shadow_color(&style, lv_color_hex(0x000000));
    lv_style_set_shadow_opa(&style, LV_OPA_20);
    
    return &style;
}

/**
 * @brief 创建按钮样式
 */
lv_style_t* ui_theme_create_button_style(void)
{
    static lv_style_t style;
    lv_style_init(&style);
    
    const ui_theme_colors_t *colors = ui_theme_get_colors(g_current_theme);
    
    lv_style_set_radius(&style, 20);
    lv_style_set_bg_color(&style, colors->primary);
    lv_style_set_bg_opa(&style, LV_OPA_COVER);
    lv_style_set_text_color(&style, lv_color_white());
    lv_style_set_pad_all(&style, 10);
    lv_style_set_shadow_width(&style, 5);
    lv_style_set_shadow_color(&style, colors->primary);
    lv_style_set_shadow_opa(&style, LV_OPA_30);
    
    return &style;
}

/**
 * @brief 创建标签样式
 */
lv_style_t* ui_theme_create_label_style(uint8_t font_size)
{
    static lv_style_t style;
    lv_style_init(&style);
    
    const ui_theme_colors_t *colors = ui_theme_get_colors(g_current_theme);
    
    lv_style_set_text_color(&style, colors->text_primary);
    
    // 根据字体大小选择字体
    if (font_size <= 12) {
        lv_style_set_text_font(&style, &lv_font_montserrat_12);
    } else if (font_size <= 14) {
        lv_style_set_text_font(&style, &lv_font_montserrat_14);
    } else if (font_size <= 16) {
        lv_style_set_text_font(&style, &lv_font_montserrat_16);
    } else if (font_size <= 18) {
        lv_style_set_text_font(&style, &lv_font_montserrat_18);
    } else {
        lv_style_set_text_font(&style, &lv_font_montserrat_20);
    }
    
    return &style;
}
