/**
 * @file lcd_driver.c
 * @brief ST7701 LCD显示屏驱动程序
 * @version 1.0.0
 * @date 2025-06-27
 * 
 * 功能特性：
 * - 480x480圆形LCD屏幕
 * - RGB565并行接口
 * - 背光PWM控制
 * - 通过GPIO扩展芯片控制复位和片选
 */

#include "lcd_driver.h"
#include "hardware_config.h"
#include "gpio_expander.h"
#include "esp_log.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_vendor.h"
#include "esp_lcd_panel_ops.h"
#include "driver/gpio.h"
#include "driver/ledc.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "LCD_DRIVER";

/* LCD控制句柄 */
static esp_lcd_panel_io_handle_t g_lcd_io_handle = NULL;
static esp_lcd_panel_handle_t g_lcd_panel_handle = NULL;
static bool g_lcd_initialized = false;

/* 背光控制 */
static uint8_t g_current_brightness = 50;  // 默认亮度50%

/**
 * @brief 初始化背光PWM
 */
static esp_err_t lcd_backlight_init(void)
{
    ESP_LOGI(TAG, "初始化LCD背光PWM...");
    
    // 配置LEDC定时器
    ledc_timer_config_t ledc_timer = {
        .speed_mode = LEDC_LOW_SPEED_MODE,
        .timer_num = LEDC_TIMER_0,
        .duty_resolution = LEDC_TIMER_8_BIT,
        .freq_hz = 5000,
        .clk_cfg = LEDC_AUTO_CLK
    };
    
    esp_err_t ret = ledc_timer_config(&ledc_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LEDC定时器配置失败");
        return ret;
    }
    
    // 配置LEDC通道
    ledc_channel_config_t ledc_channel = {
        .speed_mode = LEDC_LOW_SPEED_MODE,
        .channel = LEDC_CHANNEL_0,
        .timer_sel = LEDC_TIMER_0,
        .intr_type = LEDC_INTR_DISABLE,
        .gpio_num = LCD_BL_IO,
        .duty = 128,  // 50%亮度
        .hpoint = 0
    };
    
    ret = ledc_channel_config(&ledc_channel);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LEDC通道配置失败");
        return ret;
    }
    
    ESP_LOGI(TAG, "LCD背光PWM初始化完成");
    return ESP_OK;
}

/**
 * @brief 设置背光亮度
 */
esp_err_t lcd_set_backlight(uint8_t brightness)
{
    if (brightness > 100) {
        brightness = 100;
    }
    
    uint32_t duty = (brightness * 255) / 100;
    esp_err_t ret = ledc_set_duty(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL_0, duty);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置背光亮度失败");
        return ret;
    }
    
    ret = ledc_update_duty(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL_0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "更新背光亮度失败");
        return ret;
    }
    
    g_current_brightness = brightness;
    ESP_LOGI(TAG, "设置背光亮度: %d%%", brightness);
    
    return ESP_OK;
}

/**
 * @brief LCD复位序列
 */
static esp_err_t lcd_reset_sequence(void)
{
    ESP_LOGI(TAG, "执行LCD复位序列...");
    
    // 复位引脚拉低
    esp_err_t ret = LCD_RST_LOW();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LCD复位拉低失败");
        return ret;
    }
    
    vTaskDelay(pdMS_TO_TICKS(10));
    
    // 复位引脚拉高
    ret = LCD_RST_HIGH();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LCD复位拉高失败");
        return ret;
    }
    
    vTaskDelay(pdMS_TO_TICKS(120));
    
    ESP_LOGI(TAG, "LCD复位序列完成");
    return ESP_OK;
}

/**
 * @brief 配置RGB接口
 */
static esp_err_t lcd_rgb_interface_init(void)
{
    ESP_LOGI(TAG, "初始化LCD RGB接口...");
    
    esp_lcd_rgb_panel_config_t panel_config = {
        .data_width = 16,  // RGB565
        .psram_trans_align = 64,
        .num_fbs = 1,
        .bounce_buffer_size_px = 10 * LCD_WIDTH,
        .clk_src = LCD_CLK_SRC_PLL160M,
        .disp_gpio_num = LCD_DE_IO,
        .pclk_gpio_num = LCD_PCLK_IO,
        .vsync_gpio_num = LCD_VSYNC_IO,
        .hsync_gpio_num = LCD_HSYNC_IO,
        .de_idle_high = 0,
        .pclk_active_neg = 1,
        .data_gpio_nums = {
            // B0-B5 (蓝色)
            -1,  // B0 未连接
            LCD_B1_IO, LCD_B2_IO, LCD_B3_IO, LCD_B4_IO, LCD_B5_IO,
            // G0-G5 (绿色)
            LCD_G0_IO, LCD_G1_IO, LCD_G2_IO, LCD_G3_IO, LCD_G4_IO, LCD_G5_IO,
            // R0-R5 (红色)
            -1,  // R0 未连接
            LCD_R1_IO, LCD_R2_IO, LCD_R3_IO, LCD_R4_IO, LCD_R5_IO,
        },
        .timings = {
            .pclk_hz = 16 * 1000 * 1000,  // 16MHz像素时钟
            .h_res = LCD_WIDTH,
            .v_res = LCD_HEIGHT,
            .hsync_back_porch = 8,
            .hsync_front_porch = 8,
            .hsync_pulse_width = 4,
            .vsync_back_porch = 8,
            .vsync_front_porch = 8,
            .vsync_pulse_width = 4,
            .flags.pclk_active_neg = 1,
        },
        .flags.fb_in_psram = 1,
    };
    
    esp_err_t ret = esp_lcd_new_rgb_panel(&panel_config, &g_lcd_panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建RGB面板失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "LCD RGB接口初始化完成");
    return ESP_OK;
}

/**
 * @brief 初始化SPI接口用于发送命令
 */
static esp_err_t lcd_spi_interface_init(void)
{
    ESP_LOGI(TAG, "初始化LCD SPI命令接口...");
    
    esp_lcd_panel_io_spi_config_t io_config = {
        .dc_gpio_num = -1,  // 使用9位SPI模式
        .cs_gpio_num = -1,  // 通过GPIO扩展芯片控制
        .pclk_hz = 10 * 1000 * 1000,  // 10MHz
        .lcd_cmd_bits = 8,
        .lcd_param_bits = 8,
        .spi_mode = 0,
        .trans_queue_depth = 10,
        .on_color_trans_done = NULL,
        .user_ctx = NULL,
    };
    
    esp_err_t ret = esp_lcd_new_panel_io_spi((esp_lcd_spi_bus_handle_t)SPI_HOST_ID, 
                                            &io_config, &g_lcd_io_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建SPI IO失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "LCD SPI命令接口初始化完成");
    return ESP_OK;
}

/**
 * @brief 发送LCD初始化命令
 */
static esp_err_t lcd_send_init_commands(void)
{
    ESP_LOGI(TAG, "发送LCD初始化命令...");
    
    // 选择LCD设备
    esp_err_t ret = LCD_CS_LOW();
    if (ret != ESP_OK) {
        return ret;
    }
    
    // ST7701初始化命令序列
    // 这里需要根据具体的ST7701芯片规格书添加初始化命令
    // 以下是示例命令，实际使用时需要根据屏幕厂商提供的初始化代码调整
    
    // 软复位
    ret = esp_lcd_panel_io_tx_param(g_lcd_io_handle, 0x01, NULL, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "发送软复位命令失败");
        goto cleanup;
    }
    vTaskDelay(pdMS_TO_TICKS(120));
    
    // 退出睡眠模式
    ret = esp_lcd_panel_io_tx_param(g_lcd_io_handle, 0x11, NULL, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "发送退出睡眠命令失败");
        goto cleanup;
    }
    vTaskDelay(pdMS_TO_TICKS(120));
    
    // 设置像素格式为RGB565
    uint8_t pixel_format = 0x55;  // 16位RGB565
    ret = esp_lcd_panel_io_tx_param(g_lcd_io_handle, 0x3A, &pixel_format, 1);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置像素格式失败");
        goto cleanup;
    }
    
    // 开启显示
    ret = esp_lcd_panel_io_tx_param(g_lcd_io_handle, 0x29, NULL, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "开启显示失败");
        goto cleanup;
    }
    vTaskDelay(pdMS_TO_TICKS(50));
    
    ESP_LOGI(TAG, "LCD初始化命令发送完成");

cleanup:
    // 释放LCD片选
    LCD_CS_HIGH();
    return ret;
}

/**
 * @brief 初始化LCD显示屏
 */
esp_err_t lcd_init(void)
{
    if (g_lcd_initialized) {
        ESP_LOGW(TAG, "LCD已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化LCD显示屏...");
    
    // 初始化背光PWM
    esp_err_t ret = lcd_backlight_init();
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 执行复位序列
    ret = lcd_reset_sequence();
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 初始化SPI命令接口
    ret = lcd_spi_interface_init();
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 发送初始化命令
    ret = lcd_send_init_commands();
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 初始化RGB接口
    ret = lcd_rgb_interface_init();
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 复位面板
    ret = esp_lcd_panel_reset(g_lcd_panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LCD面板复位失败");
        return ret;
    }
    
    // 初始化面板
    ret = esp_lcd_panel_init(g_lcd_panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LCD面板初始化失败");
        return ret;
    }
    
    // 设置默认亮度
    ret = lcd_set_backlight(g_current_brightness);
    if (ret != ESP_OK) {
        return ret;
    }
    
    g_lcd_initialized = true;
    ESP_LOGI(TAG, "LCD显示屏初始化完成 (%dx%d)", LCD_WIDTH, LCD_HEIGHT);
    
    return ESP_OK;
}

/**
 * @brief 在LCD上绘制位图
 */
esp_err_t lcd_draw_bitmap(int x, int y, int width, int height, uint16_t *color_data)
{
    if (!g_lcd_initialized || !color_data) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (x < 0 || y < 0 || x + width > LCD_WIDTH || y + height > LCD_HEIGHT) {
        ESP_LOGE(TAG, "绘制区域超出屏幕范围");
        return ESP_ERR_INVALID_ARG;
    }
    
    esp_err_t ret = esp_lcd_panel_draw_bitmap(g_lcd_panel_handle, x, y, 
                                             x + width, y + height, color_data);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "绘制位图失败");
        return ret;
    }
    
    ESP_LOGD(TAG, "绘制位图: (%d,%d) %dx%d", x, y, width, height);
    return ESP_OK;
}

/**
 * @brief 填充LCD屏幕
 */
esp_err_t lcd_fill_screen(uint16_t color)
{
    if (!g_lcd_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    // 创建颜色缓冲区
    size_t buffer_size = LCD_WIDTH * sizeof(uint16_t);
    uint16_t *line_buffer = malloc(buffer_size);
    if (!line_buffer) {
        ESP_LOGE(TAG, "分配行缓冲区失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 填充行缓冲区
    for (int i = 0; i < LCD_WIDTH; i++) {
        line_buffer[i] = color;
    }
    
    // 逐行填充屏幕
    esp_err_t ret = ESP_OK;
    for (int y = 0; y < LCD_HEIGHT; y++) {
        ret = lcd_draw_bitmap(0, y, LCD_WIDTH, 1, line_buffer);
        if (ret != ESP_OK) {
            break;
        }
    }
    
    free(line_buffer);
    
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "屏幕填充完成，颜色: 0x%04X", color);
    }
    
    return ret;
}

/**
 * @brief 获取当前背光亮度
 */
uint8_t lcd_get_backlight(void)
{
    return g_current_brightness;
}

/**
 * @brief 反初始化LCD
 */
esp_err_t lcd_deinit(void)
{
    if (!g_lcd_initialized) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "反初始化LCD...");
    
    // 关闭背光
    lcd_set_backlight(0);
    
    // 删除面板
    if (g_lcd_panel_handle) {
        esp_lcd_panel_del(g_lcd_panel_handle);
        g_lcd_panel_handle = NULL;
    }
    
    // 删除IO
    if (g_lcd_io_handle) {
        esp_lcd_panel_io_del(g_lcd_io_handle);
        g_lcd_io_handle = NULL;
    }
    
    g_lcd_initialized = false;
    ESP_LOGI(TAG, "LCD反初始化完成");
    
    return ESP_OK;
}
