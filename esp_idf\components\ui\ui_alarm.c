/**
 * @file ui_alarm.c
 * @brief TIMO闹钟页面实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "ui_alarm.h"
#include "ui_theme.h"
#include "hardware_config.h"
#include "esp_log.h"

static const char *TAG = "UI_ALARM";
static lv_obj_t *g_alarm_page = NULL;

lv_obj_t* ui_alarm_create(void)
{
    ESP_LOGI(TAG, "创建闹钟页面...");
    
    g_alarm_page = lv_obj_create(NULL);
    lv_obj_set_size(g_alarm_page, LCD_WIDTH, LCD_HEIGHT);
    lv_obj_set_style_bg_color(g_alarm_page, lv_color_hex(0x2E2E2E), 0);
    
    lv_obj_t *title = lv_label_create(g_alarm_page);
    lv_label_set_text(title, "闹钟设置");
    lv_obj_add_style(title, ui_theme_create_label_style(18), 0);
    lv_obj_center(title);
    
    ESP_LOGI(TAG, "闹钟页面创建完成");
    return g_alarm_page;
}
