/**
 * @file task_scheduler.c
 * @brief TIMO任务调度器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "task_scheduler.h"
#include "esp_log.h"

static const char *TAG = "TASK_SCHEDULER";

esp_err_t task_scheduler_init(void)
{
    ESP_LOGI(TAG, "初始化任务调度器...");
    return ESP_OK;
}

esp_err_t task_scheduler_start(void)
{
    ESP_LOGI(TAG, "启动任务调度器...");
    return ESP_OK;
}

esp_err_t task_scheduler_stop(void)
{
    ESP_LOGI(TAG, "停止任务调度器...");
    return ESP_OK;
}
