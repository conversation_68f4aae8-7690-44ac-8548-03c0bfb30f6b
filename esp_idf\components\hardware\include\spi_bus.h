/**
 * @file spi_bus.h
 * @brief SPI总线驱动头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef SPI_BUS_H
#define SPI_BUS_H

#include "esp_err.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化SPI总线
 * @return esp_err_t 
 */
esp_err_t hardware_spi_init(void);

/**
 * @brief SPI传输数据
 * @param cs_pin 片选引脚（-1表示使用扩展IO）
 * @param tx_data 发送数据
 * @param rx_data 接收数据
 * @param len 数据长度
 * @return esp_err_t 
 */
esp_err_t spi_transfer(int cs_pin, uint8_t *tx_data, uint8_t *rx_data, size_t len);

/**
 * @brief 反初始化SPI总线
 * @return esp_err_t 
 */
esp_err_t spi_bus_deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* SPI_BUS_H */
