/**
 * @file touch_driver.c
 * @brief GT911触摸屏驱动程序
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "touch_driver.h"
#include "hardware_hal.h"
#include "hardware_config.h"
#include "i2c_bus.h"
#include "gpio_expander.h"
#include "esp_log.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "TOUCH_DRIVER";

/* 触摸屏状态 */
static bool g_touch_initialized = false;

/* GT911寄存器定义 */
#define GT911_REG_CONFIG        0x8047
#define GT911_REG_PRODUCT_ID    0x8140
#define GT911_REG_STATUS        0x814E
#define GT911_REG_POINT1        0x814F

/**
 * @brief 触摸屏初始化
 */
esp_err_t touch_init(void)
{
    if (g_touch_initialized) {
        ESP_LOGW(TAG, "触摸屏已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化GT911触摸屏...");
    
    if (!i2c_bus_device_online(TOUCH_GT911_I2C_ADDR)) {
        ESP_LOGE(TAG, "GT911设备未找到");
        return ESP_ERR_NOT_FOUND;
    }
    
    // 配置中断引脚
    gpio_config_t io_conf = {
        .pin_bit_mask = (1ULL << TOUCH_INT_IO),
        .mode = GPIO_MODE_INPUT,
        .pull_up_en = GPIO_PULLUP_ENABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .intr_type = GPIO_INTR_NEGEDGE
    };
    gpio_config(&io_conf);
    
    // 复位触摸屏
    esp_err_t ret = TOUCH_RST_LOW();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "触摸屏复位失败");
        return ret;
    }
    
    vTaskDelay(pdMS_TO_TICKS(10));
    
    ret = TOUCH_RST_HIGH();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "触摸屏复位释放失败");
        return ret;
    }
    
    vTaskDelay(pdMS_TO_TICKS(100));
    
    // 读取产品ID验证
    uint8_t product_id[4];
    ret = i2c_bus_read_bytes(TOUCH_GT911_I2C_ADDR, GT911_REG_PRODUCT_ID, 
                            product_id, sizeof(product_id));
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "读取触摸屏产品ID失败");
        return ret;
    }
    
    ESP_LOGI(TAG, "触摸屏产品ID: %c%c%c%c", 
             product_id[0], product_id[1], product_id[2], product_id[3]);
    
    g_touch_initialized = true;
    ESP_LOGI(TAG, "GT911触摸屏初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 读取触摸点
 */
esp_err_t touch_read_point(touch_point_t *point)
{
    if (!g_touch_initialized || !point) {
        return ESP_ERR_INVALID_STATE;
    }
    
    // 读取状态寄存器
    uint8_t status;
    esp_err_t ret = i2c_bus_read_byte(TOUCH_GT911_I2C_ADDR, GT911_REG_STATUS, &status);
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 检查是否有触摸点
    uint8_t touch_count = status & 0x0F;
    if (touch_count == 0) {
        point->pressed = false;
        return ESP_OK;
    }
    
    // 读取第一个触摸点数据
    uint8_t point_data[8];
    ret = i2c_bus_read_bytes(TOUCH_GT911_I2C_ADDR, GT911_REG_POINT1, 
                            point_data, sizeof(point_data));
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 解析触摸点坐标
    point->x = (point_data[1] << 8) | point_data[0];
    point->y = (point_data[3] << 8) | point_data[2];
    point->pressed = true;
    
    // 清除状态寄存器
    uint8_t clear_status = 0;
    i2c_bus_write_byte(TOUCH_GT911_I2C_ADDR, GT911_REG_STATUS, clear_status);
    
    ESP_LOGD(TAG, "触摸点: (%d, %d)", point->x, point->y);
    
    return ESP_OK;
}
